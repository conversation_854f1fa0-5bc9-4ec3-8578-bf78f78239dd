"""
MCP Integration Configuration

Configuration settings for MCP integration with GraphRAG + DeepSearch.
"""

import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class MCPConfig:
    """Configuration for MCP integration"""
    
    # Server configuration
    server_name: str = "GraphRAG-DeepSearch-MCP"
    server_version: str = "0.1.0"
    server_description: str = "MCP server for GraphRAG + DeepSearch system"
    
    # Network configuration
    host: str = "localhost"
    port: int = 8001
    mcp_endpoint_path: str = "/mcp"
    
    # Streaming configuration
    enable_streaming: bool = True
    stream_buffer_size: int = 1024
    stream_timeout: int = 30
    max_concurrent_streams: int = 100
    
    # Agent coordination configuration
    enable_agent_coordination: bool = True
    max_coordinated_agents: int = 5
    agent_timeout: int = 60
    coordination_strategy: str = "parallel"  # parallel, sequential, adaptive
    
    # Cache configuration
    enable_distributed_cache: bool = True
    cache_invalidation_timeout: int = 5
    cache_sync_interval: int = 10
    
    # Performance configuration
    enable_performance_monitoring: bool = True
    metrics_collection_interval: int = 1
    performance_alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "response_time_warning": 2.0,
        "response_time_critical": 5.0,
        "cache_hit_rate_warning": 0.7,
        "cache_hit_rate_critical": 0.5,
        "memory_usage_warning": 0.8,
        "memory_usage_critical": 0.9
    })
    
    # Tool configuration
    enabled_tool_categories: List[str] = field(default_factory=lambda: [
        "agent_coordination",
        "search_tools", 
        "cache_tools",
        "knowledge_graph_tools",
        "performance_monitoring"
    ])
    
    # Security configuration
    enable_authentication: bool = False
    api_key: Optional[str] = None
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    
    # Logging configuration
    log_level: str = "INFO"
    log_mcp_requests: bool = True
    log_streaming_events: bool = False
    
    # Integration configuration
    existing_fastapi_integration: bool = True
    preserve_existing_endpoints: bool = True
    
    @classmethod
    def from_env(cls) -> "MCPConfig":
        """Create configuration from environment variables"""
        return cls(
            # Server configuration
            server_name=os.getenv("MCP_SERVER_NAME", "GraphRAG-DeepSearch-MCP"),
            server_version=os.getenv("MCP_SERVER_VERSION", "0.1.0"),
            server_description=os.getenv("MCP_SERVER_DESCRIPTION", "MCP server for GraphRAG + DeepSearch system"),
            
            # Network configuration
            host=os.getenv("MCP_HOST", "localhost"),
            port=int(os.getenv("MCP_PORT", "8001")),
            mcp_endpoint_path=os.getenv("MCP_ENDPOINT_PATH", "/mcp"),
            
            # Streaming configuration
            enable_streaming=os.getenv("MCP_ENABLE_STREAMING", "true").lower() == "true",
            stream_buffer_size=int(os.getenv("MCP_STREAM_BUFFER_SIZE", "1024")),
            stream_timeout=int(os.getenv("MCP_STREAM_TIMEOUT", "30")),
            max_concurrent_streams=int(os.getenv("MCP_MAX_CONCURRENT_STREAMS", "100")),
            
            # Agent coordination configuration
            enable_agent_coordination=os.getenv("MCP_ENABLE_AGENT_COORDINATION", "true").lower() == "true",
            max_coordinated_agents=int(os.getenv("MCP_MAX_COORDINATED_AGENTS", "5")),
            agent_timeout=int(os.getenv("MCP_AGENT_TIMEOUT", "60")),
            coordination_strategy=os.getenv("MCP_COORDINATION_STRATEGY", "parallel"),
            
            # Cache configuration
            enable_distributed_cache=os.getenv("MCP_ENABLE_DISTRIBUTED_CACHE", "true").lower() == "true",
            cache_invalidation_timeout=int(os.getenv("MCP_CACHE_INVALIDATION_TIMEOUT", "5")),
            cache_sync_interval=int(os.getenv("MCP_CACHE_SYNC_INTERVAL", "10")),
            
            # Performance configuration
            enable_performance_monitoring=os.getenv("MCP_ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true",
            metrics_collection_interval=int(os.getenv("MCP_METRICS_COLLECTION_INTERVAL", "1")),
            
            # Tool configuration
            enabled_tool_categories=os.getenv("MCP_ENABLED_TOOL_CATEGORIES", 
                                            "agent_coordination,search_tools,cache_tools,knowledge_graph_tools,performance_monitoring").split(","),
            
            # Security configuration
            enable_authentication=os.getenv("MCP_ENABLE_AUTHENTICATION", "false").lower() == "true",
            api_key=os.getenv("MCP_API_KEY"),
            allowed_origins=os.getenv("MCP_ALLOWED_ORIGINS", "*").split(","),
            
            # Logging configuration
            log_level=os.getenv("MCP_LOG_LEVEL", "INFO"),
            log_mcp_requests=os.getenv("MCP_LOG_REQUESTS", "true").lower() == "true",
            log_streaming_events=os.getenv("MCP_LOG_STREAMING_EVENTS", "false").lower() == "true",
            
            # Integration configuration
            existing_fastapi_integration=os.getenv("MCP_EXISTING_FASTAPI_INTEGRATION", "true").lower() == "true",
            preserve_existing_endpoints=os.getenv("MCP_PRESERVE_EXISTING_ENDPOINTS", "true").lower() == "true"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "server": {
                "name": self.server_name,
                "version": self.server_version,
                "description": self.server_description
            },
            "network": {
                "host": self.host,
                "port": self.port,
                "mcp_endpoint_path": self.mcp_endpoint_path
            },
            "streaming": {
                "enable_streaming": self.enable_streaming,
                "stream_buffer_size": self.stream_buffer_size,
                "stream_timeout": self.stream_timeout,
                "max_concurrent_streams": self.max_concurrent_streams
            },
            "agent_coordination": {
                "enable_agent_coordination": self.enable_agent_coordination,
                "max_coordinated_agents": self.max_coordinated_agents,
                "agent_timeout": self.agent_timeout,
                "coordination_strategy": self.coordination_strategy
            },
            "cache": {
                "enable_distributed_cache": self.enable_distributed_cache,
                "cache_invalidation_timeout": self.cache_invalidation_timeout,
                "cache_sync_interval": self.cache_sync_interval
            },
            "performance": {
                "enable_performance_monitoring": self.enable_performance_monitoring,
                "metrics_collection_interval": self.metrics_collection_interval,
                "performance_alert_thresholds": self.performance_alert_thresholds
            },
            "tools": {
                "enabled_tool_categories": self.enabled_tool_categories
            },
            "security": {
                "enable_authentication": self.enable_authentication,
                "api_key": self.api_key,
                "allowed_origins": self.allowed_origins
            },
            "logging": {
                "log_level": self.log_level,
                "log_mcp_requests": self.log_mcp_requests,
                "log_streaming_events": self.log_streaming_events
            },
            "integration": {
                "existing_fastapi_integration": self.existing_fastapi_integration,
                "preserve_existing_endpoints": self.preserve_existing_endpoints
            }
        }
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Validate port range
        if not (1024 <= self.port <= 65535):
            errors.append(f"Port {self.port} is not in valid range (1024-65535)")
        
        # Validate coordination strategy
        valid_strategies = ["parallel", "sequential", "adaptive"]
        if self.coordination_strategy not in valid_strategies:
            errors.append(f"Invalid coordination strategy: {self.coordination_strategy}. Must be one of {valid_strategies}")
        
        # Validate tool categories
        valid_categories = [
            "agent_coordination", "search_tools", "cache_tools", 
            "knowledge_graph_tools", "performance_monitoring"
        ]
        for category in self.enabled_tool_categories:
            if category not in valid_categories:
                errors.append(f"Invalid tool category: {category}. Must be one of {valid_categories}")
        
        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            errors.append(f"Invalid log level: {self.log_level}. Must be one of {valid_log_levels}")
        
        # Validate performance thresholds
        for metric, threshold in self.performance_alert_thresholds.items():
            if not isinstance(threshold, (int, float)) or threshold < 0:
                errors.append(f"Invalid threshold for {metric}: {threshold}. Must be a positive number")
        
        return errors
    
    def get_tool_config(self, tool_category: str) -> Dict[str, Any]:
        """Get configuration for specific tool category"""
        base_config = {
            "enabled": tool_category in self.enabled_tool_categories,
            "timeout": self.agent_timeout if tool_category == "agent_coordination" else 30,
            "log_level": self.log_level
        }
        
        if tool_category == "agent_coordination":
            base_config.update({
                "max_coordinated_agents": self.max_coordinated_agents,
                "coordination_strategy": self.coordination_strategy,
                "agent_timeout": self.agent_timeout
            })
        elif tool_category == "search_tools":
            base_config.update({
                "enable_streaming": self.enable_streaming,
                "stream_timeout": self.stream_timeout
            })
        elif tool_category == "cache_tools":
            base_config.update({
                "enable_distributed_cache": self.enable_distributed_cache,
                "cache_invalidation_timeout": self.cache_invalidation_timeout,
                "cache_sync_interval": self.cache_sync_interval
            })
        elif tool_category == "performance_monitoring":
            base_config.update({
                "enable_performance_monitoring": self.enable_performance_monitoring,
                "metrics_collection_interval": self.metrics_collection_interval,
                "performance_alert_thresholds": self.performance_alert_thresholds
            })
        
        return base_config


# Global configuration instance
mcp_config = MCPConfig.from_env()


def get_mcp_config() -> MCPConfig:
    """Get global MCP configuration instance"""
    return mcp_config


def update_mcp_config(**kwargs) -> None:
    """Update global MCP configuration"""
    global mcp_config
    for key, value in kwargs.items():
        if hasattr(mcp_config, key):
            setattr(mcp_config, key, value)
        else:
            raise ValueError(f"Invalid configuration key: {key}")


def validate_mcp_config() -> None:
    """Validate global MCP configuration and raise exception if invalid"""
    errors = mcp_config.validate()
    if errors:
        raise ValueError(f"Invalid MCP configuration: {'; '.join(errors)}")


# Configuration validation on import
try:
    validate_mcp_config()
except ValueError as e:
    print(f"Warning: {e}")
    print("Using default configuration values where possible")