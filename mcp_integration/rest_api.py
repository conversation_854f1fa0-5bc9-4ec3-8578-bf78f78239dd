"""
REST API Wrapper for MCP Tools

This module provides REST API endpoints that can trigger MCP tools for debugging,
testing, and integration purposes. It creates a bridge between REST API calls
and the MCP protocol.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport, SSETransport

from .config import MCPConfig


class ToolCallRequest(BaseModel):
    """Request model for calling MCP tools via REST API"""
    tool_name: str = Field(..., description="Name of the MCP tool to call")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="Arguments to pass to the tool")
    transport: str = Field(default="streamable-http", description="Transport protocol to use (sse, streamable-http)")


class ToolCallResponse(BaseModel):
    """Response model for MCP tool calls"""
    success: bool = Field(..., description="Whether the tool call was successful")
    result: Optional[Any] = Field(None, description="Tool execution result")
    error: Optional[str] = Field(None, description="Error message if call failed")
    tool_name: str = Field(..., description="Name of the tool that was called")
    transport_used: str = Field(..., description="Transport protocol that was used")
    execution_time_ms: Optional[float] = Field(None, description="Execution time in milliseconds")


class ToolListResponse(BaseModel):
    """Response model for listing available tools"""
    tools: List[Dict[str, Any]] = Field(..., description="List of available MCP tools")
    total_count: int = Field(..., description="Total number of tools")
    transport_used: str = Field(..., description="Transport protocol that was used")


class MCPHealthResponse(BaseModel):
    """Response model for MCP health check"""
    status: str = Field(..., description="Health status")
    transports: Dict[str, str] = Field(..., description="Available transport endpoints")
    tools_count: int = Field(..., description="Number of available tools")
    server_info: Dict[str, Any] = Field(..., description="MCP server information")


class MCPRestAPI:
    """REST API wrapper for MCP tools"""
    
    def __init__(self, config: MCPConfig):
        """
        Initialize MCP REST API wrapper
        
        Args:
            config: MCP configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"mcp_rest_api.{config.server_name}")
        self.router = APIRouter(prefix="/api/v1/mcp", tags=["MCP Tools"])
        
        # Setup routes
        self._setup_routes()
        
        self.logger.info("MCP REST API initialized")
    
    def _setup_routes(self):
        """Setup REST API routes"""
        
        @self.router.get("/health", response_model=MCPHealthResponse)
        async def mcp_health():
            """Get MCP server health and status"""
            try:
                # Test connection to both transports
                tools_count = await self._get_tools_count()
                
                return MCPHealthResponse(
                    status="healthy",
                    transports={
                        "sse": f"http://{self.config.host}:{self.config.port}{self.config.mcp_endpoint_path}/sse",
                        "streamable_http": f"http://{self.config.host}:{self.config.port}{self.config.mcp_endpoint_path}/http"
                    },
                    tools_count=tools_count,
                    server_info={
                        "name": self.config.server_name,
                        "version": self.config.server_version,
                        "host": self.config.host,
                        "port": self.config.port
                    }
                )
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                raise HTTPException(status_code=503, detail=f"MCP server unhealthy: {str(e)}")
        
        @self.router.get("/tools", response_model=ToolListResponse)
        async def list_tools(transport: str = "streamable-http"):
            """List all available MCP tools"""
            try:
                tools_data = await self._list_tools(transport)
                return ToolListResponse(
                    tools=tools_data,
                    total_count=len(tools_data),
                    transport_used=transport
                )
            except Exception as e:
                self.logger.error(f"Failed to list tools: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to list tools: {str(e)}")
        
        @self.router.post("/tools/call", response_model=ToolCallResponse)
        async def call_tool(request: ToolCallRequest, background_tasks: BackgroundTasks):
            """Call an MCP tool via REST API"""
            import time
            start_time = time.time()
            
            try:
                result = await self._call_tool(
                    tool_name=request.tool_name,
                    arguments=request.arguments,
                    transport=request.transport
                )
                
                execution_time = (time.time() - start_time) * 1000
                
                return ToolCallResponse(
                    success=True,
                    result=result,
                    tool_name=request.tool_name,
                    transport_used=request.transport,
                    execution_time_ms=execution_time
                )
                
            except Exception as e:
                execution_time = (time.time() - start_time) * 1000
                self.logger.error(f"Tool call failed: {e}")
                
                return ToolCallResponse(
                    success=False,
                    error=str(e),
                    tool_name=request.tool_name,
                    transport_used=request.transport,
                    execution_time_ms=execution_time
                )
        
        @self.router.get("/tools/{tool_name}")
        async def get_tool_info(tool_name: str, transport: str = "streamable-http"):
            """Get information about a specific MCP tool"""
            try:
                tools = await self._list_tools(transport)
                tool_info = next((tool for tool in tools if tool.get("name") == tool_name), None)
                
                if not tool_info:
                    raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
                
                return {
                    "tool": tool_info,
                    "transport_used": transport
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Failed to get tool info: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to get tool info: {str(e)}")
        
        # Convenience endpoints for specific tool categories
        @self.router.post("/search")
        async def search_local(request: dict, transport: str = "streamable-http"):
            """Convenience endpoint for local search"""
            return await call_tool(ToolCallRequest(
                tool_name="local_search",
                arguments={
                    "query": request.get("query"),
                    "max_results": request.get("max_results", 5)
                },
                transport=transport
            ), BackgroundTasks())
        
        @self.router.get("/agent/{agent_id}/status")
        async def get_agent_status(
            agent_id: str,
            transport: str = "streamable-http"
        ):
            """Convenience endpoint for agent status"""
            result = await call_tool(ToolCallRequest(
                tool_name="get_agent_status",
                arguments={"agent_id": agent_id},
                transport=transport
            ), BackgroundTasks())
            return result
        
        @self.router.post("/cache/set")
        async def set_cache_entry(request: dict, transport: str = "streamable-http"):
            """Convenience endpoint for setting cache entries"""
            return await call_tool(ToolCallRequest(
                tool_name="set_cache_entry",
                arguments={
                    "cache_key": request.get("cache_key"),
                    "value": request.get("value"),
                    "cache_type": request.get("cache_type", "default")
                },
                transport=transport
            ), BackgroundTasks())
        
        @self.router.get("/cache/{cache_key}")
        async def get_cache_entry(
            cache_key: str,
            cache_type: str = "default",
            transport: str = "streamable-http"
        ):
            """Convenience endpoint for getting cache entries"""
            result = await call_tool(ToolCallRequest(
                tool_name="get_cache_entry",
                arguments={
                    "cache_key": cache_key,
                    "cache_type": cache_type
                },
                transport=transport
            ), BackgroundTasks())
            return result
    
    async def _get_transport_client(self, transport: str) -> Client:
        """Get MCP client for specified transport"""
        base_url = f"http://{self.config.host}:{self.config.port}{self.config.mcp_endpoint_path}"
        
        if transport == "sse":
            transport_obj = SSETransport(url=f"{base_url}/sse")
        elif transport == "streamable-http":
            transport_obj = StreamableHttpTransport(url=f"{base_url}/http")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
        
        return Client(transport_obj)
    
    async def _list_tools(self, transport: str) -> List[Dict[str, Any]]:
        """List all available MCP tools"""
        client = await self._get_transport_client(transport)
        
        async with client:
            tools = await client.list_tools()
            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema if tool.inputSchema else None
                }
                for tool in tools
            ]
    
    async def _call_tool(self, tool_name: str, arguments: Dict[str, Any], transport: str) -> Any:
        """Call an MCP tool and return the result"""
        client = await self._get_transport_client(transport)
        
        async with client:
            result = await client.call_tool(tool_name, arguments)
            
            # Parse the result
            if result and len(result) > 0:
                # Try to parse as JSON if it's text content
                if hasattr(result[0], 'text'):
                    try:
                        return json.loads(result[0].text)
                    except json.JSONDecodeError:
                        return result[0].text
                elif hasattr(result[0], 'data'):
                    return {"binary_data_length": len(result[0].data)}
                else:
                    return str(result[0])
            
            return None
    
    async def _get_tools_count(self) -> int:
        """Get the number of available tools"""
        try:
            tools = await self._list_tools("streamable-http")
            return len(tools)
        except Exception:
            # Fallback to SSE if streamable-http fails
            try:
                tools = await self._list_tools("sse")
                return len(tools)
            except Exception:
                return 0


def create_mcp_rest_api(config: MCPConfig) -> APIRouter:
    """
    Factory function to create MCP REST API router
    
    Args:
        config: MCP configuration
        
    Returns:
        FastAPI router with MCP REST endpoints
    """
    api = MCPRestAPI(config)
    return api.router