"""
Streaming Manager for MCP Integration

This module handles real-time streaming capabilities for the GraphRAG MCP server,
including Server-Sent Events (SSE) and WebSocket-like functionality.
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, List, Optional, AsyncGenerator, Set
from dataclasses import dataclass, field
from collections import defaultdict
import logging

from .config import MCPConfig


@dataclass
class StreamSession:
    """Represents an active streaming session"""
    session_id: str
    client_id: str
    stream_type: str
    created_at: float
    last_activity: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True


class StreamingManager:
    """
    Manages real-time streaming for MCP integration
    
    Handles Server-Sent Events (SSE), WebSocket-like communication,
    and real-time data streaming for various GraphRAG components.
    """
    
    def __init__(self, config: MCPConfig):
        """
        Initialize Streaming Manager
        
        Args:
            config: MCP configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"mcp_streaming.{config.server_name}")
        
        # Active streaming sessions
        self.active_sessions: Dict[str, StreamSession] = {}
        self.client_sessions: Dict[str, Set[str]] = defaultdict(set)
        
        # Stream queues for different types
        self.stream_queues: Dict[str, asyncio.Queue] = {}
        self.stream_subscribers: Dict[str, Set[str]] = defaultdict(set)
        
        # Performance metrics
        self.metrics = {
            "total_streams_created": 0,
            "active_stream_count": 0,
            "total_messages_sent": 0,
            "average_message_size": 0,
            "stream_errors": 0
        }
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
        
        self.logger.info("Streaming Manager initialized")
    
    async def start(self):
        """Start the streaming manager and background tasks"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Start cleanup task
        cleanup_task = asyncio.create_task(self._cleanup_inactive_sessions())
        self.background_tasks.append(cleanup_task)
        
        # Start metrics collection task
        if self.config.enable_performance_monitoring:
            metrics_task = asyncio.create_task(self._collect_streaming_metrics())
            self.background_tasks.append(metrics_task)
        
        self.logger.info("Streaming Manager started")
    
    async def stop(self):
        """Stop the streaming manager and cleanup resources"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # Close all active sessions
        await self.close_all_streams()
        
        self.logger.info("Streaming Manager stopped")
    
    def create_stream_session(
        self,
        client_id: str,
        stream_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new streaming session
        
        Args:
            client_id: Unique client identifier
            stream_type: Type of stream (e.g., 'agent_coordination', 'search_results')
            metadata: Additional session metadata
            
        Returns:
            Session ID for the new stream
        """
        session_id = str(uuid.uuid4())
        current_time = time.time()
        
        session = StreamSession(
            session_id=session_id,
            client_id=client_id,
            stream_type=stream_type,
            created_at=current_time,
            last_activity=current_time,
            metadata=metadata or {}
        )
        
        self.active_sessions[session_id] = session
        self.client_sessions[client_id].add(session_id)
        
        # Create queue for this stream type if it doesn't exist
        if stream_type not in self.stream_queues:
            self.stream_queues[stream_type] = asyncio.Queue(
                maxsize=self.config.stream_buffer_size
            )
        
        # Subscribe session to stream type
        self.stream_subscribers[stream_type].add(session_id)
        
        # Update metrics
        self.metrics["total_streams_created"] += 1
        self.metrics["active_stream_count"] = len(self.active_sessions)
        
        self.logger.debug(f"Created stream session {session_id} for client {client_id}, type {stream_type}")
        return session_id
    
    async def close_stream_session(self, session_id: str):
        """
        Close a streaming session
        
        Args:
            session_id: Session ID to close
        """
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        # Remove from subscribers
        self.stream_subscribers[session.stream_type].discard(session_id)
        
        # Remove from client sessions
        self.client_sessions[session.client_id].discard(session_id)
        if not self.client_sessions[session.client_id]:
            del self.client_sessions[session.client_id]
        
        # Remove session
        del self.active_sessions[session_id]
        
        # Update metrics
        self.metrics["active_stream_count"] = len(self.active_sessions)
        
        self.logger.debug(f"Closed stream session {session_id}")
    
    async def close_all_streams(self):
        """Close all active streaming sessions"""
        session_ids = list(self.active_sessions.keys())
        for session_id in session_ids:
            await self.close_stream_session(session_id)
        
        self.logger.info("All streaming sessions closed")
    
    async def send_to_stream(
        self,
        stream_type: str,
        data: Dict[str, Any],
        target_session_id: Optional[str] = None
    ):
        """
        Send data to a stream
        
        Args:
            stream_type: Type of stream to send to
            data: Data to send
            target_session_id: Specific session to send to (None for broadcast)
        """
        if stream_type not in self.stream_subscribers:
            return
        
        # Add metadata to data
        message = {
            "timestamp": time.time(),
            "stream_type": stream_type,
            "data": data,
            "message_id": str(uuid.uuid4())
        }
        
        # Determine target sessions
        if target_session_id and target_session_id in self.stream_subscribers[stream_type]:
            target_sessions = {target_session_id}
        else:
            target_sessions = self.stream_subscribers[stream_type].copy()
        
        # Send to target sessions
        for session_id in target_sessions:
            if session_id in self.active_sessions:
                try:
                    # Update session activity
                    self.active_sessions[session_id].last_activity = time.time()
                    
                    # Add to queue (this would be consumed by the actual streaming endpoint)
                    if stream_type in self.stream_queues:
                        try:
                            self.stream_queues[stream_type].put_nowait({
                                "session_id": session_id,
                                "message": message
                            })
                        except asyncio.QueueFull:
                            self.logger.warning(f"Stream queue full for {stream_type}, dropping message")
                            self.metrics["stream_errors"] += 1
                    
                except Exception as e:
                    self.logger.error(f"Error sending to stream {session_id}: {e}")
                    self.metrics["stream_errors"] += 1
        
        # Update metrics
        self.metrics["total_messages_sent"] += len(target_sessions)
        message_size = len(json.dumps(message))
        self.metrics["average_message_size"] = (
            (self.metrics["average_message_size"] * (self.metrics["total_messages_sent"] - len(target_sessions)) + 
             message_size * len(target_sessions)) / self.metrics["total_messages_sent"]
        )
    
    async def stream_generator(
        self,
        session_id: str,
        stream_type: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate streaming data for a specific session
        
        Args:
            session_id: Session ID
            stream_type: Type of stream
            
        Yields:
            Streaming data messages
        """
        if session_id not in self.active_sessions:
            return
        
        if stream_type not in self.stream_queues:
            return
        
        queue = self.stream_queues[stream_type]
        
        try:
            while (session_id in self.active_sessions and 
                   self.active_sessions[session_id].is_active):
                
                try:
                    # Wait for message with timeout
                    message_data = await asyncio.wait_for(
                        queue.get(),
                        timeout=self.config.stream_timeout
                    )
                    
                    # Check if message is for this session
                    if message_data["session_id"] == session_id:
                        yield message_data["message"]
                    else:
                        # Put message back for other sessions
                        queue.put_nowait(message_data)
                
                except asyncio.TimeoutError:
                    # Send keepalive message
                    yield {
                        "timestamp": time.time(),
                        "stream_type": "keepalive",
                        "data": {"status": "alive"},
                        "message_id": str(uuid.uuid4())
                    }
                
                except Exception as e:
                    self.logger.error(f"Error in stream generator for {session_id}: {e}")
                    break
        
        finally:
            # Cleanup session
            await self.close_stream_session(session_id)
    
    async def stream_agent_coordination(
        self,
        session_id: str,
        agents: List[str],
        query: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream agent coordination updates
        
        Args:
            session_id: Session ID
            agents: List of agent IDs
            query: Query being processed
            
        Yields:
            Agent coordination updates
        """
        for agent_id in agents:
            # Agent started
            await self.send_to_stream(
                "agent_coordination",
                {
                    "agent_id": agent_id,
                    "status": "started",
                    "query": query,
                    "message": f"Agent {agent_id} started processing"
                },
                session_id
            )
            
            # Simulate processing time
            await asyncio.sleep(0.5)
            
            # Agent processing
            await self.send_to_stream(
                "agent_coordination",
                {
                    "agent_id": agent_id,
                    "status": "processing",
                    "query": query,
                    "progress": 50,
                    "message": f"Agent {agent_id} processing query"
                },
                session_id
            )
            
            await asyncio.sleep(0.5)
            
            # Agent completed
            await self.send_to_stream(
                "agent_coordination",
                {
                    "agent_id": agent_id,
                    "status": "completed",
                    "query": query,
                    "result": f"Agent {agent_id} completed successfully",
                    "confidence": 0.85
                },
                session_id
            )
        
        # Stream from the generator
        async for message in self.stream_generator(session_id, "agent_coordination"):
            yield message
    
    async def stream_search_results(
        self,
        session_id: str,
        query: str,
        search_type: str,
        max_results: int = 10
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream search results as they become available
        
        Args:
            session_id: Session ID
            query: Search query
            search_type: Type of search (local, global, hybrid)
            max_results: Maximum number of results
            
        Yields:
            Search result updates
        """
        # Simulate search results
        for i in range(min(max_results, 5)):
            result = {
                "result_id": f"{search_type}_result_{i}",
                "content": f"{search_type.title()} search result {i} for: {query}",
                "relevance_score": 1.0 - (i * 0.1),
                "search_type": search_type,
                "source": f"document_{i}.pdf"
            }
            
            await self.send_to_stream(
                "search_results",
                result,
                session_id
            )
            
            await asyncio.sleep(0.1)  # Simulate processing time
        
        # Stream from the generator
        async for message in self.stream_generator(session_id, "search_results"):
            yield message
    
    async def stream_knowledge_graph_exploration(
        self,
        session_id: str,
        start_node: str,
        max_depth: int = 3
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream knowledge graph exploration results
        
        Args:
            session_id: Session ID
            start_node: Starting node for exploration
            max_depth: Maximum exploration depth
            
        Yields:
            Graph exploration updates
        """
        # Simulate graph exploration
        nodes_to_explore = [(start_node, 0)]
        explored_nodes = set()
        
        while nodes_to_explore and len(explored_nodes) < 20:
            current_node, depth = nodes_to_explore.pop(0)
            
            if current_node in explored_nodes or depth > max_depth:
                continue
            
            explored_nodes.add(current_node)
            
            # Send node data
            await self.send_to_stream(
                "knowledge_graph",
                {
                    "element_type": "node",
                    "node_id": current_node,
                    "depth": depth,
                    "properties": {
                        "name": current_node,
                        "type": "concept",
                        "description": f"Description of {current_node}"
                    }
                },
                session_id
            )
            
            # Add connected nodes
            if depth < max_depth:
                connected_nodes = [f"{current_node}_child_{i}" for i in range(2)]
                for connected_node in connected_nodes:
                    nodes_to_explore.append((connected_node, depth + 1))
                    
                    # Send relationship data
                    await self.send_to_stream(
                        "knowledge_graph",
                        {
                            "element_type": "relationship",
                            "source_node": current_node,
                            "target_node": connected_node,
                            "relationship_type": "RELATED_TO",
                            "properties": {"strength": 0.8}
                        },
                        session_id
                    )
            
            await asyncio.sleep(0.1)
        
        # Stream from the generator
        async for message in self.stream_generator(session_id, "knowledge_graph"):
            yield message
    
    async def stream_performance_metrics(
        self,
        session_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream real-time performance metrics
        
        Args:
            session_id: Session ID
            
        Yields:
            Performance metric updates
        """
        metrics = [
            "response_time", "cache_hit_rate", "active_connections",
            "memory_usage", "cpu_usage", "disk_io"
        ]
        
        counter = 0
        while session_id in self.active_sessions:
            for metric_name in metrics:
                metric_data = {
                    "metric_name": metric_name,
                    "value": 50 + (counter % 10) * 5,  # Simulate varying values
                    "unit": "ms" if "time" in metric_name else "percent",
                    "timestamp": time.time()
                }
                
                await self.send_to_stream(
                    "performance_metrics",
                    metric_data,
                    session_id
                )
            
            counter += 1
            await asyncio.sleep(self.config.metrics_collection_interval)
        
        # Stream from the generator
        async for message in self.stream_generator(session_id, "performance_metrics"):
            yield message
    
    async def stream_server_events(self) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream server events and status updates
        
        Yields:
            Server event updates
        """
        while self.is_running:
            event = {
                "event_type": "server_status",
                "timestamp": time.time(),
                "data": {
                    "active_sessions": len(self.active_sessions),
                    "total_streams_created": self.metrics["total_streams_created"],
                    "total_messages_sent": self.metrics["total_messages_sent"],
                    "stream_errors": self.metrics["stream_errors"]
                }
            }
            yield event
            await asyncio.sleep(5)  # Send status every 5 seconds
    
    async def _cleanup_inactive_sessions(self):
        """Background task to cleanup inactive sessions"""
        while self.is_running:
            try:
                current_time = time.time()
                inactive_sessions = []
                
                for session_id, session in self.active_sessions.items():
                    if (current_time - session.last_activity) > self.config.stream_timeout:
                        inactive_sessions.append(session_id)
                
                for session_id in inactive_sessions:
                    await self.close_stream_session(session_id)
                    self.logger.debug(f"Cleaned up inactive session {session_id}")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in session cleanup: {e}")
                await asyncio.sleep(30)
    
    async def _collect_streaming_metrics(self):
        """Background task to collect streaming metrics"""
        while self.is_running:
            try:
                # Update active stream count
                self.metrics["active_stream_count"] = len(self.active_sessions)
                
                # Log metrics periodically
                if self.config.log_streaming_events:
                    self.logger.info(f"Streaming metrics: {self.metrics}")
                
                await asyncio.sleep(self.config.metrics_collection_interval)
                
            except Exception as e:
                self.logger.error(f"Error collecting streaming metrics: {e}")
                await asyncio.sleep(self.config.metrics_collection_interval)
    
    def get_active_stream_count(self) -> int:
        """Get number of active streaming sessions"""
        return len(self.active_sessions)
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific session"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        return {
            "session_id": session.session_id,
            "client_id": session.client_id,
            "stream_type": session.stream_type,
            "created_at": session.created_at,
            "last_activity": session.last_activity,
            "metadata": session.metadata,
            "is_active": session.is_active
        }
    
    def get_streaming_metrics(self) -> Dict[str, Any]:
        """Get current streaming metrics"""
        return self.metrics.copy()