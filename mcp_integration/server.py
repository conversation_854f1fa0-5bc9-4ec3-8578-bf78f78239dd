"""
GraphRAG MCP Server Implementation

This module implements the FastMCP server for GraphRAG + DeepSearch integration,
providing real-time communication capabilities and tool coordination.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import FastAPI, HTTPException
from fastmcp import FastMC<PERSON>
# FastMCP HTTP apps are created using mcp_app.http_app() method

from .config import MCPConfig, get_mcp_config
from .tools import (
    AgentCoordinationTools,
    SearchTools, 
    CacheTools,
    KnowledgeGraphTools,
    PerformanceMonitoringTools
)
from .streaming import StreamingManager
from .middleware import MCPMiddleware


class GraphRAGMCPServer:
    """
    GraphRAG MCP Server
    
    Integrates FastMCP with the existing GraphRAG + DeepSearch system,
    providing real-time communication and tool coordination capabilities.
    """
    
    def __init__(self, config: Optional[MCPConfig] = None):
        """
        Initialize GraphRAG MCP Server
        
        Args:
            config: MCP configuration (uses global config if None)
        """
        self.config = config or get_mcp_config()
        self.logger = self._setup_logging()
        
        # Initialize FastMCP app
        self.mcp_app = FastMCP(
            name=self.config.server_name,
            version=self.config.server_version,
        )
        
        # Initialize components
        self.streaming_manager = StreamingManager(self.config)
        self.middleware = MCPMiddleware(self.config)
        
        # Initialize tool managers
        self.tool_managers = {}
        self._initialize_tool_managers()
        
        # Register tools
        self._register_tools()
        
        # Setup middleware
        self._setup_middleware()
        
        self.logger.info(f"GraphRAG MCP Server initialized: {self.config.server_name}")

    def get_mcp_app(self) -> FastMCP:
        """Get the FastMCP app instance for TDD integration"""
        return self.mcp_app

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for MCP server"""
        logger = logging.getLogger(f"mcp_server.{self.config.server_name}")
        logger.setLevel(getattr(logging, self.config.log_level))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _initialize_tool_managers(self):
        """Initialize tool managers based on configuration"""
        if "agent_coordination" in self.config.enabled_tool_categories:
            self.tool_managers["agent_coordination"] = AgentCoordinationTools(
                self.config.get_tool_config("agent_coordination")
            )
        
        if "search_tools" in self.config.enabled_tool_categories:
            self.tool_managers["search_tools"] = SearchTools(
                self.config.get_tool_config("search_tools")
            )
        
        if "cache_tools" in self.config.enabled_tool_categories:
            self.tool_managers["cache_tools"] = CacheTools(
                self.config.get_tool_config("cache_tools")
            )
        
        if "knowledge_graph_tools" in self.config.enabled_tool_categories:
            self.tool_managers["knowledge_graph_tools"] = KnowledgeGraphTools(
                self.config.get_tool_config("knowledge_graph_tools")
            )
        
        if "performance_monitoring" in self.config.enabled_tool_categories:
            self.tool_managers["performance_monitoring"] = PerformanceMonitoringTools(
                self.config.get_tool_config("performance_monitoring")
            )
        
        self.logger.info(f"Initialized tool managers: {list(self.tool_managers.keys())}")
    
    def _register_tools(self):
        """Register all tools with the MCP app"""
        for category, tool_manager in self.tool_managers.items():
            self.logger.info(f"Registering tools for category: {category}")
            tool_manager.register_tools(self.mcp_app)
        
        # Register server management tools
        self._register_server_tools()
        
        self.logger.info("All tools registered successfully")
    
    def _register_server_tools(self):
        """Register server management tools"""
        
        @self.mcp_app.tool()
        async def get_server_info() -> Dict[str, Any]:
            """Get server information and status"""
            return {
                "server_name": self.config.server_name,
                "server_version": self.config.server_version,
                "server_description": self.config.server_description,
                "enabled_tool_categories": self.config.enabled_tool_categories,
                "configuration": self.config.to_dict(),
                "status": "running",
                "active_streams": self.streaming_manager.get_active_stream_count(),
                "tool_count": len(self.mcp_app.tools)
            }
        
        @self.mcp_app.tool()
        async def list_available_tools() -> List[Dict[str, Any]]:
            """List all available tools with their descriptions"""
            tools = []
            for tool_name, tool in self.mcp_app.tools.items():
                tools.append({
                    "name": tool_name,
                    "description": tool.description or "No description available",
                    "parameters": tool.parameters_schema if hasattr(tool, 'parameters_schema') else {},
                    "category": self._get_tool_category(tool_name)
                })
            return tools
        
        @self.mcp_app.tool()
        async def get_tool_usage_stats() -> Dict[str, Any]:
            """Get tool usage statistics"""
            # This would be implemented with actual usage tracking
            return {
                "total_tool_calls": 0,
                "tool_call_frequency": {},
                "average_response_time": {},
                "error_rates": {}
            }
        
        if self.config.enable_streaming:
            @self.mcp_app.tool()
            async def stream_server_events() -> AsyncGenerator[Dict[str, Any], None]:
                """Stream server events and status updates"""
                async for event in self.streaming_manager.stream_server_events():
                    yield event
    
    def _get_tool_category(self, tool_name: str) -> str:
        """Get category for a tool based on its name"""
        if any(keyword in tool_name for keyword in ["agent", "coordinate", "assign"]):
            return "agent_coordination"
        elif any(keyword in tool_name for keyword in ["search", "query", "retrieve"]):
            return "search_tools"
        elif any(keyword in tool_name for keyword in ["cache", "invalidate", "store"]):
            return "cache_tools"
        elif any(keyword in tool_name for keyword in ["graph", "node", "relationship", "explore"]):
            return "knowledge_graph_tools"
        elif any(keyword in tool_name for keyword in ["performance", "metrics", "monitor"]):
            return "performance_monitoring"
        else:
            return "server_management"
    
    def _setup_middleware(self):
        """Setup middleware for the MCP app"""
        # Add logging middleware
        if self.config.log_mcp_requests:
            self.middleware.add_request_logging()
        
        # Add performance monitoring middleware
        if self.config.enable_performance_monitoring:
            self.middleware.add_performance_monitoring()
        
        # Add authentication middleware if enabled
        if self.config.enable_authentication:
            self.middleware.add_authentication(self.config.api_key)
    
    def integrate_with_fastapi(self, app: FastAPI) -> FastAPI:
        """
        Integrate MCP server with existing FastAPI application

        Args:
            app: Existing FastAPI application

        Returns:
            FastAPI application with MCP integration
        """
        self.logger.info(f"Integrating MCP server with FastAPI at {self.config.mcp_endpoint_path}")

        # TDD FIX 5: Create SSE app for legacy transport
        mcp_sse_app = self.mcp_app.http_app(
            path="/",  # Root path since we're mounting at /mcp/sse
            transport="sse"
        )

        # TDD FIX 6: Use cached HTTP app if available to avoid creating different instances
        if hasattr(self, '_cached_http_app') and self._cached_http_app:
            mcp_http_app = self._cached_http_app
            self.logger.info("✅ Using cached Streamable HTTP app for consistent lifespan")
        else:
            # Fallback: create new HTTP app (should not happen with TDD approach)
            mcp_http_app = self.mcp_app.http_app(
                path="/",  # Root path since we're mounting at /mcp/http
                transport="streamable-http"
            )
            self.logger.warning("⚠️ Created new Streamable HTTP app (cached app not found)")

        # Note: FastAPI app should be created with MCP lifespan for Streamable HTTP to work
        # This is handled in run_mcp_server.py during app creation

        # Mount both MCP transports with proper lifespan handling
        # SSE transport at /mcp/sse (legacy)
        app.mount(f"{self.config.mcp_endpoint_path}/sse", mcp_sse_app)

        # Streamable HTTP transport at /mcp/http (recommended)
        app.mount(f"{self.config.mcp_endpoint_path}/http", mcp_http_app)

        self.logger.info(f"✅ SSE Transport: {self.config.mcp_endpoint_path}/sse")
        self.logger.info(f"✅ Streamable HTTP Transport: {self.config.mcp_endpoint_path}/http")
        
        # Add CORS middleware if needed
        if "*" in self.config.allowed_origins or len(self.config.allowed_origins) > 1:
            from fastapi.middleware.cors import CORSMiddleware
            app.add_middleware(
                CORSMiddleware,
                allow_origins=self.config.allowed_origins,
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # Add health check endpoint for MCP
        @app.get(f"{self.config.mcp_endpoint_path}/health")
        async def mcp_health_check():
            """Health check endpoint for MCP server"""
            return {
                "status": "healthy",
                "server_name": self.config.server_name,
                "version": self.config.server_version,
                "transports": {
                    "sse": f"{self.config.mcp_endpoint_path}/sse",
                    "streamable_http": f"{self.config.mcp_endpoint_path}/http"
                },
                "active_streams": self.streaming_manager.get_active_stream_count(),
                "enabled_tools": len(self.mcp_app.tools)
            }
        
        # Add REST API endpoints for MCP tools
        from .rest_api import create_mcp_rest_api
        rest_api_router = create_mcp_rest_api(self.config)
        app.include_router(rest_api_router)
        
        self.logger.info("✅ REST API endpoints added for MCP tools")
        self.logger.info("MCP integration with FastAPI completed")
        return app
    
    async def start_background_tasks(self):
        """Start background tasks for the MCP server"""
        tasks = []
        
        # Start streaming manager
        if self.config.enable_streaming:
            tasks.append(asyncio.create_task(self.streaming_manager.start()))
        
        # Start performance monitoring
        if self.config.enable_performance_monitoring and "performance_monitoring" in self.tool_managers:
            tasks.append(asyncio.create_task(
                self.tool_managers["performance_monitoring"].start_monitoring()
            ))
        
        # Start cache synchronization
        if self.config.enable_distributed_cache and "cache_tools" in self.tool_managers:
            tasks.append(asyncio.create_task(
                self.tool_managers["cache_tools"].start_cache_sync()
            ))
        
        self.logger.info(f"Started {len(tasks)} background tasks")
        return tasks
    
    async def stop_background_tasks(self):
        """Stop background tasks for the MCP server"""
        # Stop streaming manager
        if self.config.enable_streaming:
            await self.streaming_manager.stop()
        
        # Stop tool managers
        for tool_manager in self.tool_managers.values():
            if hasattr(tool_manager, 'stop'):
                await tool_manager.stop()
        
        self.logger.info("All background tasks stopped")
    
    def get_mcp_app(self) -> FastMCP:
        """Get the underlying FastMCP application"""
        return self.mcp_app
    
    def get_tool_manager(self, category: str) -> Optional[Any]:
        """Get tool manager for specific category"""
        return self.tool_managers.get(category)
    
    def get_streaming_manager(self) -> StreamingManager:
        """Get the streaming manager"""
        return self.streaming_manager
    
    async def shutdown(self):
        """Gracefully shutdown the MCP server"""
        self.logger.info("Shutting down GraphRAG MCP Server...")
        
        # Stop background tasks
        await self.stop_background_tasks()
        
        # Close any remaining connections
        await self.streaming_manager.close_all_streams()
        
        self.logger.info("GraphRAG MCP Server shutdown completed")


# Factory function for easy server creation
def create_graphrag_mcp_server(
    config: Optional[MCPConfig] = None,
    existing_fastapi_app: Optional[FastAPI] = None
) -> GraphRAGMCPServer:
    """
    Factory function to create and configure GraphRAG MCP Server
    
    Args:
        config: MCP configuration (uses global config if None)
        existing_fastapi_app: Existing FastAPI app to integrate with
        
    Returns:
        Configured GraphRAG MCP Server
    """
    server = GraphRAGMCPServer(config)
    
    if existing_fastapi_app:
        server.integrate_with_fastapi(existing_fastapi_app)
    
    return server


# Convenience function for standalone server
async def run_standalone_mcp_server(config: Optional[MCPConfig] = None):
    """
    Run GraphRAG MCP Server as standalone application
    
    Args:
        config: MCP configuration (uses global config if None)
    """
    import uvicorn
    from fastapi import FastAPI
    
    # Create FastAPI app
    app = FastAPI(
        title="GraphRAG + DeepSearch MCP Server",
        description="Model Context Protocol server for GraphRAG + DeepSearch system",
        version="0.1.0"
    )
    
    # Create and integrate MCP server
    mcp_server = create_graphrag_mcp_server(config, app)
    
    # Start background tasks
    background_tasks = await mcp_server.start_background_tasks()
    
    try:
        # Run the server
        config = config or get_mcp_config()
        uvicorn.run(
            app,
            host=config.host,
            port=config.port,
            log_level=config.log_level.lower()
        )
    finally:
        # Cleanup
        await mcp_server.shutdown()


if __name__ == "__main__":
    # Run standalone server
    asyncio.run(run_standalone_mcp_server())