"""
MCP Middleware Components

This module provides middleware components for the GraphRAG MCP server,
including logging, authentication, performance monitoring, and error handling.
"""

import time
import logging
import async<PERSON>
from typing import Dict, Any, Optional, Callable, Awaitable
from functools import wraps
import json

from .config import MCPConfig


class MCPMiddleware:
    """
    Middleware manager for MCP server
    
    Provides various middleware components for logging, authentication,
    performance monitoring, and error handling.
    """
    
    def __init__(self, config: MCPConfig):
        """
        Initialize MCP Middleware
        
        Args:
            config: MCP configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"mcp_middleware.{config.server_name}")
        
        # Performance metrics
        self.request_metrics = {
            "total_requests": 0,
            "total_response_time": 0.0,
            "error_count": 0,
            "request_sizes": [],
            "response_sizes": []
        }
        
        # Authentication state
        self.authenticated_clients = set()
        
        self.logger.info("MCP Middleware initialized")
    
    def add_request_logging(self):
        """Add request logging middleware"""
        def logging_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                request_id = f"req_{int(start_time * 1000)}"
                
                # Log request
                if self.config.log_mcp_requests:
                    self.logger.info(
                        f"[{request_id}] MCP Request: {func.__name__} "
                        f"args={args} kwargs={kwargs}"
                    )
                
                try:
                    # Execute function
                    result = await func(*args, **kwargs)
                    
                    # Log successful response
                    response_time = time.time() - start_time
                    if self.config.log_mcp_requests:
                        self.logger.info(
                            f"[{request_id}] MCP Response: {func.__name__} "
                            f"completed in {response_time:.3f}s"
                        )
                    
                    return result
                    
                except Exception as e:
                    # Log error
                    response_time = time.time() - start_time
                    self.logger.error(
                        f"[{request_id}] MCP Error: {func.__name__} "
                        f"failed after {response_time:.3f}s - {str(e)}"
                    )
                    raise
            
            return wrapper
        
        return logging_middleware
    
    def add_performance_monitoring(self):
        """Add performance monitoring middleware"""
        def performance_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                
                # Calculate request size (approximate)
                request_size = len(str(args)) + len(str(kwargs))
                
                try:
                    # Execute function
                    result = await func(*args, **kwargs)
                    
                    # Calculate metrics
                    response_time = time.time() - start_time
                    response_size = len(str(result)) if result else 0
                    
                    # Update metrics
                    self.request_metrics["total_requests"] += 1
                    self.request_metrics["total_response_time"] += response_time
                    self.request_metrics["request_sizes"].append(request_size)
                    self.request_metrics["response_sizes"].append(response_size)
                    
                    # Keep only recent metrics (last 1000 requests)
                    if len(self.request_metrics["request_sizes"]) > 1000:
                        self.request_metrics["request_sizes"] = self.request_metrics["request_sizes"][-1000:]
                        self.request_metrics["response_sizes"] = self.request_metrics["response_sizes"][-1000:]
                    
                    # Check performance thresholds
                    await self._check_performance_alerts(response_time, func.__name__)
                    
                    return result
                    
                except Exception as e:
                    # Update error metrics
                    self.request_metrics["error_count"] += 1
                    raise
            
            return wrapper
        
        return performance_middleware
    
    def add_authentication(self, api_key: Optional[str] = None):
        """Add authentication middleware"""
        def auth_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Extract client identifier (this would be more sophisticated in practice)
                client_id = kwargs.get("client_id") or "unknown"
                
                # Check if authentication is required
                if self.config.enable_authentication and api_key:
                    # Check if client is authenticated
                    provided_key = kwargs.get("api_key")
                    
                    if provided_key != api_key:
                        self.logger.warning(f"Authentication failed for client {client_id}")
                        raise PermissionError("Invalid API key")
                    
                    # Mark client as authenticated
                    self.authenticated_clients.add(client_id)
                
                # Execute function
                return await func(*args, **kwargs)
            
            return wrapper
        
        return auth_middleware
    
    def add_rate_limiting(self, max_requests_per_minute: int = 60):
        """Add rate limiting middleware"""
        client_request_counts = {}
        
        def rate_limit_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                client_id = kwargs.get("client_id") or "unknown"
                current_time = time.time()
                
                # Initialize client tracking
                if client_id not in client_request_counts:
                    client_request_counts[client_id] = []
                
                # Clean old requests (older than 1 minute)
                client_request_counts[client_id] = [
                    req_time for req_time in client_request_counts[client_id]
                    if current_time - req_time < 60
                ]
                
                # Check rate limit
                if len(client_request_counts[client_id]) >= max_requests_per_minute:
                    self.logger.warning(f"Rate limit exceeded for client {client_id}")
                    raise Exception("Rate limit exceeded")
                
                # Add current request
                client_request_counts[client_id].append(current_time)
                
                # Execute function
                return await func(*args, **kwargs)
            
            return wrapper
        
        return rate_limit_middleware
    
    def add_error_handling(self):
        """Add error handling middleware"""
        def error_handling_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                    
                except asyncio.TimeoutError:
                    self.logger.error(f"Timeout in {func.__name__}")
                    return {
                        "error": "timeout",
                        "message": "Request timed out",
                        "function": func.__name__
                    }
                    
                except PermissionError as e:
                    self.logger.error(f"Permission error in {func.__name__}: {e}")
                    return {
                        "error": "permission_denied",
                        "message": str(e),
                        "function": func.__name__
                    }
                    
                except ValueError as e:
                    self.logger.error(f"Value error in {func.__name__}: {e}")
                    return {
                        "error": "invalid_input",
                        "message": str(e),
                        "function": func.__name__
                    }
                    
                except Exception as e:
                    self.logger.error(f"Unexpected error in {func.__name__}: {e}")
                    return {
                        "error": "internal_error",
                        "message": "An internal error occurred",
                        "function": func.__name__
                    }
            
            return wrapper
        
        return error_handling_middleware
    
    def add_caching_middleware(self, cache_manager=None):
        """Add caching middleware"""
        def caching_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                if not cache_manager:
                    return await func(*args, **kwargs)
                
                # Generate cache key
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
                
                # Try to get from cache
                cached_result = await cache_manager.get(cache_key)
                if cached_result is not None:
                    self.logger.debug(f"Cache hit for {func.__name__}")
                    return cached_result
                
                # Execute function
                result = await func(*args, **kwargs)
                
                # Cache the result
                await cache_manager.set(cache_key, result, ttl=300)  # 5 minute TTL
                self.logger.debug(f"Cached result for {func.__name__}")
                
                return result
            
            return wrapper
        
        return caching_middleware
    
    def add_input_validation(self, validation_schema: Optional[Dict[str, Any]] = None):
        """Add input validation middleware"""
        def validation_middleware(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Basic validation (could be enhanced with JSON schema)
                if validation_schema:
                    for param, rules in validation_schema.items():
                        if param in kwargs:
                            value = kwargs[param]
                            
                            # Type validation
                            if "type" in rules and not isinstance(value, rules["type"]):
                                raise ValueError(f"Parameter {param} must be of type {rules['type'].__name__}")
                            
                            # Range validation for numbers
                            if "min" in rules and isinstance(value, (int, float)) and value < rules["min"]:
                                raise ValueError(f"Parameter {param} must be >= {rules['min']}")
                            
                            if "max" in rules and isinstance(value, (int, float)) and value > rules["max"]:
                                raise ValueError(f"Parameter {param} must be <= {rules['max']}")
                            
                            # Length validation for strings/lists
                            if "min_length" in rules and hasattr(value, "__len__") and len(value) < rules["min_length"]:
                                raise ValueError(f"Parameter {param} must have length >= {rules['min_length']}")
                            
                            if "max_length" in rules and hasattr(value, "__len__") and len(value) > rules["max_length"]:
                                raise ValueError(f"Parameter {param} must have length <= {rules['max_length']}")
                
                return await func(*args, **kwargs)
            
            return wrapper
        
        return validation_middleware
    
    async def _check_performance_alerts(self, response_time: float, function_name: str):
        """Check performance thresholds and generate alerts"""
        thresholds = self.config.performance_alert_thresholds
        
        # Check response time thresholds
        if response_time > thresholds.get("response_time_critical", 5.0):
            self.logger.critical(
                f"CRITICAL: Response time {response_time:.3f}s for {function_name} "
                f"exceeds critical threshold {thresholds['response_time_critical']}s"
            )
        elif response_time > thresholds.get("response_time_warning", 2.0):
            self.logger.warning(
                f"WARNING: Response time {response_time:.3f}s for {function_name} "
                f"exceeds warning threshold {thresholds['response_time_warning']}s"
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        total_requests = self.request_metrics["total_requests"]
        
        if total_requests == 0:
            return {
                "total_requests": 0,
                "average_response_time": 0,
                "error_rate": 0,
                "average_request_size": 0,
                "average_response_size": 0
            }
        
        return {
            "total_requests": total_requests,
            "average_response_time": self.request_metrics["total_response_time"] / total_requests,
            "error_rate": self.request_metrics["error_count"] / total_requests,
            "average_request_size": sum(self.request_metrics["request_sizes"]) / len(self.request_metrics["request_sizes"]),
            "average_response_size": sum(self.request_metrics["response_sizes"]) / len(self.request_metrics["response_sizes"]),
            "error_count": self.request_metrics["error_count"]
        }
    
    def reset_metrics(self):
        """Reset performance metrics"""
        self.request_metrics = {
            "total_requests": 0,
            "total_response_time": 0.0,
            "error_count": 0,
            "request_sizes": [],
            "response_sizes": []
        }
        self.logger.info("Performance metrics reset")
    
    def get_authenticated_clients(self) -> set:
        """Get set of authenticated client IDs"""
        return self.authenticated_clients.copy()
    
    def revoke_client_authentication(self, client_id: str):
        """Revoke authentication for a specific client"""
        self.authenticated_clients.discard(client_id)
        self.logger.info(f"Revoked authentication for client {client_id}")


class StreamingMiddleware:
    """
    Specialized middleware for streaming operations
    
    Handles streaming-specific concerns like connection management,
    backpressure, and stream health monitoring.
    """
    
    def __init__(self, config: MCPConfig):
        """
        Initialize Streaming Middleware
        
        Args:
            config: MCP configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"mcp_streaming_middleware.{config.server_name}")
        
        # Stream health metrics
        self.stream_metrics = {
            "active_streams": 0,
            "total_streams_created": 0,
            "stream_errors": 0,
            "messages_sent": 0,
            "bytes_transferred": 0
        }
    
    def add_stream_health_monitoring(self):
        """Add stream health monitoring middleware"""
        def stream_health_middleware(stream_func: Callable) -> Callable:
            @wraps(stream_func)
            async def wrapper(*args, **kwargs):
                stream_id = kwargs.get("session_id", "unknown")
                
                try:
                    self.stream_metrics["active_streams"] += 1
                    self.stream_metrics["total_streams_created"] += 1
                    
                    self.logger.debug(f"Stream {stream_id} started")
                    
                    # Execute streaming function
                    async for item in stream_func(*args, **kwargs):
                        # Monitor message size
                        message_size = len(json.dumps(item)) if isinstance(item, dict) else len(str(item))
                        self.stream_metrics["messages_sent"] += 1
                        self.stream_metrics["bytes_transferred"] += message_size
                        
                        yield item
                    
                    self.logger.debug(f"Stream {stream_id} completed successfully")
                    
                except Exception as e:
                    self.stream_metrics["stream_errors"] += 1
                    self.logger.error(f"Stream {stream_id} error: {e}")
                    raise
                    
                finally:
                    self.stream_metrics["active_streams"] -= 1
            
            return wrapper
        
        return stream_health_middleware
    
    def add_backpressure_control(self, max_queue_size: int = 100):
        """Add backpressure control middleware"""
        def backpressure_middleware(stream_func: Callable) -> Callable:
            @wraps(stream_func)
            async def wrapper(*args, **kwargs):
                queue = asyncio.Queue(maxsize=max_queue_size)
                
                async def producer():
                    try:
                        async for item in stream_func(*args, **kwargs):
                            await queue.put(item)
                    except Exception as e:
                        await queue.put({"error": str(e)})
                    finally:
                        await queue.put(None)  # End marker
                
                # Start producer task
                producer_task = asyncio.create_task(producer())
                
                try:
                    while True:
                        item = await queue.get()
                        if item is None:  # End marker
                            break
                        
                        if isinstance(item, dict) and "error" in item:
                            raise Exception(item["error"])
                        
                        yield item
                        
                finally:
                    producer_task.cancel()
                    try:
                        await producer_task
                    except asyncio.CancelledError:
                        pass
            
            return wrapper
        
        return backpressure_middleware
    
    def add_stream_timeout(self, timeout_seconds: int = 30):
        """Add stream timeout middleware"""
        def timeout_middleware(stream_func: Callable) -> Callable:
            @wraps(stream_func)
            async def wrapper(*args, **kwargs):
                try:
                    async with asyncio.timeout(timeout_seconds):
                        async for item in stream_func(*args, **kwargs):
                            yield item
                            
                except asyncio.TimeoutError:
                    self.logger.warning(f"Stream timeout after {timeout_seconds}s")
                    yield {
                        "error": "stream_timeout",
                        "message": f"Stream timed out after {timeout_seconds} seconds"
                    }
            
            return wrapper
        
        return timeout_middleware
    
    def get_stream_metrics(self) -> Dict[str, Any]:
        """Get current stream metrics"""
        return self.stream_metrics.copy()
    
    def reset_stream_metrics(self):
        """Reset stream metrics"""
        self.stream_metrics = {
            "active_streams": 0,
            "total_streams_created": 0,
            "stream_errors": 0,
            "messages_sent": 0,
            "bytes_transferred": 0
        }
        self.logger.info("Stream metrics reset")