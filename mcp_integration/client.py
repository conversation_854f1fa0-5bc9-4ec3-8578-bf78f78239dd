"""
GraphRAG MCP Client Implementation

This module implements the MCP client for connecting to GraphRAG MCP servers
and consuming their tools and streaming capabilities.
"""

import asyncio
import logging
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
import httpx
from fastmcp import Client

from .config import MCPConfig, get_mcp_config


class GraphRAGMCPClient:
    """
    GraphRAG MCP Client
    
    Provides client functionality for connecting to GraphRAG MCP servers,
    calling tools, and consuming streaming responses.
    """
    
    def __init__(self, server_url: str, config: Optional[MCPConfig] = None):
        """
        Initialize GraphRAG MCP Client
        
        Args:
            server_url: URL of the MCP server
            config: MCP configuration (uses global config if None)
        """
        self.server_url = server_url.rstrip('/')
        self.config = config or get_mcp_config()
        self.logger = self._setup_logging()
        
        # Client state
        self.client = None
        self.connected = False
        self.available_tools = {}
        
        # HTTP client for streaming
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        self.logger.info(f"GraphRAG MCP Client initialized for server: {server_url}")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for MCP client"""
        logger = logging.getLogger(f"mcp_client.{self.config.server_name}")
        logger.setLevel(getattr(logging, self.config.log_level))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def connect(self) -> bool:
        """
        Connect to the MCP server
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Create MCP client
            self.client = Client(self.server_url)
            
            # Test connection by getting server info
            server_info = await self.call_tool("get_server_info")
            if server_info and not server_info.get("error"):
                self.connected = True
                self.logger.info(f"Connected to MCP server: {server_info.get('server_name', 'Unknown')}")
                
                # Load available tools
                await self._load_available_tools()
                
                return True
            else:
                self.logger.error(f"Failed to get server info: {server_info}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the MCP server"""
        if self.client:
            try:
                await self.client.close()
            except Exception as e:
                self.logger.error(f"Error closing MCP client: {e}")
        
        if self.http_client:
            await self.http_client.aclose()
        
        self.connected = False
        self.client = None
        self.logger.info("Disconnected from MCP server")
    
    async def _load_available_tools(self):
        """Load list of available tools from the server"""
        try:
            tools_list = await self.call_tool("list_available_tools")
            if tools_list and not tools_list.get("error"):
                self.available_tools = {
                    tool["name"]: tool for tool in tools_list
                }
                self.logger.info(f"Loaded {len(self.available_tools)} available tools")
            else:
                self.logger.warning("Failed to load available tools")
                
        except Exception as e:
            self.logger.error(f"Error loading available tools: {e}")
    
    async def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """
        Call a tool on the MCP server
        
        Args:
            tool_name: Name of the tool to call
            **kwargs: Tool parameters
            
        Returns:
            Tool response
        """
        if not self.connected or not self.client:
            return {"error": True, "message": "Not connected to MCP server"}
        
        try:
            self.logger.debug(f"Calling tool: {tool_name} with args: {kwargs}")
            
            # Call tool using MCP client
            response = await self.client.call_tool(tool_name, **kwargs)
            
            if self.config.log_mcp_requests:
                self.logger.info(f"Tool call successful: {tool_name}")
            
            return response
            
        except Exception as e:
            error_response = {
                "error": True,
                "message": f"Tool call failed: {str(e)}",
                "tool_name": tool_name
            }
            self.logger.error(f"Tool call failed: {tool_name} - {e}")
            return error_response
    
    async def stream_tool_response(self, tool_name: str, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream responses from a tool that supports streaming
        
        Args:
            tool_name: Name of the streaming tool
            **kwargs: Tool parameters
            
        Yields:
            Streaming response data
        """
        if not self.connected:
            yield {"error": True, "message": "Not connected to MCP server"}
            return
        
        try:
            self.logger.debug(f"Starting stream for tool: {tool_name}")
            
            # Use HTTP client for streaming (SSE)
            stream_url = f"{self.server_url}{self.config.mcp_endpoint_path}/stream/{tool_name}"
            
            async with self.http_client.stream(
                "POST",
                stream_url,
                json=kwargs,
                headers={"Accept": "text/event-stream"}
            ) as response:
                
                if response.status_code != 200:
                    yield {
                        "error": True,
                        "message": f"Stream request failed: {response.status_code}",
                        "tool_name": tool_name
                    }
                    return
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # Remove "data: " prefix
                            yield data
                        except json.JSONDecodeError as e:
                            self.logger.error(f"Failed to parse streaming data: {e}")
                            continue
                    elif line.startswith("event: "):
                        # Handle SSE events if needed
                        continue
                        
        except Exception as e:
            yield {
                "error": True,
                "message": f"Streaming failed: {str(e)}",
                "tool_name": tool_name
            }
            self.logger.error(f"Streaming failed for tool {tool_name}: {e}")
    
    async def search_local(self, query: str, max_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Convenience method for local search
        
        Args:
            query: Search query
            max_results: Maximum number of results
            **kwargs: Additional search parameters
            
        Returns:
            Search results
        """
        return await self.call_tool(
            "local_search",
            query=query,
            max_results=max_results,
            **kwargs
        )
    
    async def search_global(self, query: str, community_level: int = 2, **kwargs) -> Dict[str, Any]:
        """
        Convenience method for global search
        
        Args:
            query: Search query
            community_level: Community level for search
            **kwargs: Additional search parameters
            
        Returns:
            Search results
        """
        return await self.call_tool(
            "global_search",
            query=query,
            community_level=community_level,
            **kwargs
        )
    
    async def search_hybrid(self, query: str, local_weight: float = 0.6, global_weight: float = 0.4, **kwargs) -> Dict[str, Any]:
        """
        Convenience method for hybrid search
        
        Args:
            query: Search query
            local_weight: Weight for local search results
            global_weight: Weight for global search results
            **kwargs: Additional search parameters
            
        Returns:
            Search results
        """
        return await self.call_tool(
            "hybrid_search",
            query=query,
            local_weight=local_weight,
            global_weight=global_weight,
            **kwargs
        )
    
    async def stream_search_results(self, query: str, search_type: str = "local", **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream search results as they become available
        
        Args:
            query: Search query
            search_type: Type of search (local, global, hybrid)
            **kwargs: Additional search parameters
            
        Yields:
            Search result updates
        """
        async for result in self.stream_tool_response(
            "stream_search_results",
            query=query,
            search_type=search_type,
            **kwargs
        ):
            yield result
    
    async def coordinate_agents(self, query: str, agents: List[str], strategy: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Coordinate multiple agents for query processing
        
        Args:
            query: Query to process
            agents: List of agent IDs
            strategy: Coordination strategy
            
        Yields:
            Agent coordination updates
        """
        async for update in self.stream_tool_response(
            "coordinate_agents",
            query=query,
            agents=agents,
            strategy=strategy
        ):
            yield update
    
    async def explore_knowledge_graph(self, start_node: str, max_depth: int = 3, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Explore knowledge graph starting from a node
        
        Args:
            start_node: Starting node for exploration
            max_depth: Maximum exploration depth
            **kwargs: Additional exploration parameters
            
        Yields:
            Graph exploration updates
        """
        async for update in self.stream_tool_response(
            "explore_knowledge_graph",
            start_node=start_node,
            max_depth=max_depth,
            **kwargs
        ):
            yield update
    
    async def get_cache_entry(self, cache_key: str, cache_type: str = "query_results") -> Dict[str, Any]:
        """
        Get entry from cache
        
        Args:
            cache_key: Cache key
            cache_type: Type of cache
            
        Returns:
            Cache entry or miss information
        """
        return await self.call_tool(
            "get_cache_entry",
            cache_key=cache_key,
            cache_type=cache_type
        )
    
    async def set_cache_entry(self, cache_key: str, value: Any, cache_type: str = "query_results", **kwargs) -> Dict[str, Any]:
        """
        Set entry in cache
        
        Args:
            cache_key: Cache key
            value: Value to cache
            cache_type: Type of cache
            **kwargs: Additional cache parameters (ttl, metadata)
            
        Returns:
            Cache operation result
        """
        return await self.call_tool(
            "set_cache_entry",
            cache_key=cache_key,
            value=value,
            cache_type=cache_type,
            **kwargs
        )
    
    async def stream_performance_metrics(self, metric_types: Optional[List[str]] = None, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream real-time performance metrics
        
        Args:
            metric_types: Types of metrics to stream
            **kwargs: Additional streaming parameters
            
        Yields:
            Performance metric updates
        """
        async for metrics in self.stream_tool_response(
            "stream_performance_metrics",
            metric_types=metric_types,
            **kwargs
        ):
            yield metrics
    
    async def get_server_status(self) -> Dict[str, Any]:
        """
        Get server status and information
        
        Returns:
            Server status information
        """
        return await self.call_tool("get_server_info")
    
    async def health_check(self) -> bool:
        """
        Perform health check on the server
        
        Returns:
            True if server is healthy, False otherwise
        """
        try:
            # Try to get server info
            response = await self.get_server_status()
            return not response.get("error", False)
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """
        Get list of available tools
        
        Returns:
            Dictionary of available tools with their metadata
        """
        return self.available_tools.copy()
    
    def is_connected(self) -> bool:
        """Check if client is connected to server"""
        return self.connected
    
    def get_server_url(self) -> str:
        """Get the server URL"""
        return self.server_url


# Factory function for easy client creation
async def create_graphrag_mcp_client(
    server_url: str,
    config: Optional[MCPConfig] = None,
    auto_connect: bool = True
) -> GraphRAGMCPClient:
    """
    Factory function to create and optionally connect GraphRAG MCP Client
    
    Args:
        server_url: URL of the MCP server
        config: MCP configuration (uses global config if None)
        auto_connect: Whether to automatically connect to the server
        
    Returns:
        GraphRAG MCP Client instance
    """
    client = GraphRAGMCPClient(server_url, config)
    
    if auto_connect:
        connected = await client.connect()
        if not connected:
            raise ConnectionError(f"Failed to connect to MCP server at {server_url}")
    
    return client


# Context manager for automatic connection management
class GraphRAGMCPClientContext:
    """Context manager for GraphRAG MCP Client with automatic connection management"""
    
    def __init__(self, server_url: str, config: Optional[MCPConfig] = None):
        self.server_url = server_url
        self.config = config
        self.client = None
    
    async def __aenter__(self) -> GraphRAGMCPClient:
        self.client = await create_graphrag_mcp_client(
            self.server_url,
            self.config,
            auto_connect=True
        )
        return self.client
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.disconnect()


# Convenience function for context manager usage
def mcp_client_context(server_url: str, config: Optional[MCPConfig] = None) -> GraphRAGMCPClientContext:
    """
    Create a context manager for GraphRAG MCP Client
    
    Args:
        server_url: URL of the MCP server
        config: MCP configuration
        
    Returns:
        Context manager for MCP client
        
    Usage:
        async with mcp_client_context("http://localhost:8001") as client:
            result = await client.search_local("AI applications")
    """
    return GraphRAGMCPClientContext(server_url, config)