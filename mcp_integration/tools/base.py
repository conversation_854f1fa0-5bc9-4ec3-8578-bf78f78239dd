"""
Base Tool Manager

This module provides the base class for all MCP tool managers,
defining common functionality and interfaces.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from fastmcp import FastMCP


class BaseToolManager(ABC):
    """
    Base class for MCP tool managers
    
    Provides common functionality for tool registration, configuration,
    and lifecycle management.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize base tool manager
        
        Args:
            config: Tool-specific configuration
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.logger = logging.getLogger(f"mcp_tools.{self.__class__.__name__}")
        
        # Tool registry
        self.registered_tools = {}
        
        self.logger.info(f"Initialized {self.__class__.__name__} (enabled: {self.enabled})")
    
    @abstractmethod
    def register_tools(self, mcp_app: FastMCP):
        """
        Register tools with the MCP application
        
        Args:
            mcp_app: FastMCP application instance
        """
        pass
    
    def is_enabled(self) -> bool:
        """Check if this tool manager is enabled"""
        return self.enabled
    
    def get_tool_count(self) -> int:
        """Get number of registered tools"""
        return len(self.registered_tools)
    
    def get_tool_names(self) -> list:
        """Get list of registered tool names"""
        return list(self.registered_tools.keys())
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value with default"""
        return self.config.get(key, default)
    
    async def start(self):
        """Start any background tasks for this tool manager"""
        pass
    
    async def stop(self):
        """Stop any background tasks for this tool manager"""
        pass
    
    def _register_tool(self, mcp_app: FastMCP, tool_name: str, tool_func):
        """
        Helper method to register a tool and track it
        
        Args:
            mcp_app: FastMCP application instance
            tool_name: Name of the tool
            tool_func: Tool function to register
        """
        if not self.enabled:
            self.logger.debug(f"Skipping registration of {tool_name} (manager disabled)")
            return
        
        # Register with MCP app
        mcp_app.tool(name=tool_name)(tool_func)
        
        # Track in our registry
        self.registered_tools[tool_name] = tool_func
        
        self.logger.debug(f"Registered tool: {tool_name}")
    
    def _log_tool_call(self, tool_name: str, **kwargs):
        """Log tool call for debugging"""
        if self.config.get("log_level") == "DEBUG":
            self.logger.debug(f"Tool call: {tool_name} with args: {kwargs}")
    
    def _handle_tool_error(self, tool_name: str, error: Exception) -> Dict[str, Any]:
        """
        Handle tool execution errors
        
        Args:
            tool_name: Name of the tool that failed
            error: Exception that occurred
            
        Returns:
            Error response dictionary
        """
        self.logger.error(f"Error in tool {tool_name}: {str(error)}")
        
        return {
            "error": True,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "tool_name": tool_name
        }