"""
Search Tools for MCP Integration

This module provides MCP tools for search functionality,
including local search, global search, and hybrid search capabilities.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastmcp import FastMCP

from .base import BaseToolManager


class SearchTools(BaseToolManager):
    """
    Search Tool Manager
    
    Provides tools for various search operations including local search,
    global search, hybrid search, and streaming search results.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Search Tools
        
        Args:
            config: Search tools configuration
        """
        super().__init__(config)
        
        # Search configuration
        self.enable_streaming = config.get("enable_streaming", True)
        self.stream_timeout = config.get("stream_timeout", 30)
        self.default_max_results = config.get("default_max_results", 10)
        
        # Search metrics
        self.search_metrics = {
            "total_searches": 0,
            "local_searches": 0,
            "global_searches": 0,
            "hybrid_searches": 0,
            "average_response_time": 0.0,
            "cache_hit_rate": 0.0
        }
        
        self.logger.info(f"Search tools configured: streaming={self.enable_streaming}")
    
    def register_tools(self, mcp_app: FastMCP):
        """Register search tools with MCP app"""
        if not self.enabled:
            return
        
        @mcp_app.tool()
        async def local_search(
            query: str,
            max_results: int = 10,
            community_level: Optional[int] = None
        ) -> Dict[str, Any]:
            """Execute local search using existing LocalSearchTool"""
            self._log_tool_call("local_search", query=query, max_results=max_results)
            
            try:
                start_time = time.time()
                
                # Simulate local search results
                results = []
                for i in range(min(max_results, 5)):
                    results.append({
                        "id": f"local_result_{i}",
                        "content": f"Local search result {i} for: {query}",
                        "score": 0.9 - (i * 0.1),
                        "source": f"document_{i}.pdf",
                        "community_id": f"community_{i % 3}",
                        "metadata": {
                            "document_type": "pdf",
                            "page_number": i + 1,
                            "section": f"section_{i}"
                        }
                    })
                
                execution_time = time.time() - start_time
                
                # Update metrics
                self.search_metrics["total_searches"] += 1
                self.search_metrics["local_searches"] += 1
                self._update_average_response_time(execution_time)
                
                return {
                    "query": query,
                    "results": results,
                    "total_results": len(results),
                    "search_type": "local",
                    "execution_time": execution_time,
                    "community_level": community_level,
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("local_search", e)
        
        @mcp_app.tool()
        async def global_search(
            query: str,
            community_level: int = 2,
            response_type: str = "multiple paragraphs"
        ) -> Dict[str, Any]:
            """Execute global search using existing GlobalSearchTool"""
            self._log_tool_call("global_search", query=query, community_level=community_level)
            
            try:
                start_time = time.time()
                
                # Simulate global search results
                communities = []
                for i in range(3):
                    communities.append({
                        "community_id": f"community_{i}",
                        "summary": f"Community {i} summary for query: {query}",
                        "relevance_score": 0.8 - (i * 0.1),
                        "entities": [f"entity_{i}_{j}" for j in range(3)],
                        "relationships": [f"rel_{i}_{j}" for j in range(2)],
                        "document_count": 10 - i,
                        "metadata": {
                            "community_size": 50 - (i * 10),
                            "avg_entity_degree": 5.5 - (i * 0.5)
                        }
                    })
                
                execution_time = time.time() - start_time
                
                # Update metrics
                self.search_metrics["total_searches"] += 1
                self.search_metrics["global_searches"] += 1
                self._update_average_response_time(execution_time)
                
                return {
                    "query": query,
                    "communities": communities,
                    "global_answer": f"Global analysis of {query} across knowledge graph communities",
                    "search_type": "global",
                    "community_level": community_level,
                    "response_type": response_type,
                    "execution_time": execution_time,
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("global_search", e)
        
        @mcp_app.tool()
        async def hybrid_search(
            query: str,
            local_weight: float = 0.6,
            global_weight: float = 0.4,
            max_results: int = 15
        ) -> Dict[str, Any]:
            """Execute hybrid search combining local and global results"""
            self._log_tool_call("hybrid_search", query=query, local_weight=local_weight, global_weight=global_weight)
            
            try:
                start_time = time.time()
                
                # Get local results
                local_results = await local_search(query, max_results=max_results//2)
                
                # Get global results
                global_results = await global_search(query)
                
                # Combine and weight results
                combined_results = []
                
                # Add weighted local results
                for result in local_results["results"]:
                    result["weighted_score"] = result["score"] * local_weight
                    result["source_type"] = "local"
                    combined_results.append(result)
                
                # Add weighted global results
                for community in global_results["communities"]:
                    combined_results.append({
                        "id": f"global_{community['community_id']}",
                        "content": community["summary"],
                        "score": community["relevance_score"],
                        "weighted_score": community["relevance_score"] * global_weight,
                        "source_type": "global",
                        "community_id": community["community_id"],
                        "metadata": community["metadata"]
                    })
                
                # Sort by weighted score
                combined_results.sort(key=lambda x: x["weighted_score"], reverse=True)
                
                execution_time = time.time() - start_time
                
                # Update metrics
                self.search_metrics["total_searches"] += 1
                self.search_metrics["hybrid_searches"] += 1
                self._update_average_response_time(execution_time)
                
                return {
                    "query": query,
                    "results": combined_results[:max_results],
                    "local_weight": local_weight,
                    "global_weight": global_weight,
                    "search_type": "hybrid",
                    "local_result_count": len(local_results["results"]),
                    "global_result_count": len(global_results["communities"]),
                    "execution_time": execution_time,
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("hybrid_search", e)
        
        if self.enable_streaming:
            @mcp_app.tool()
            async def stream_search_results(
                query: str,
                search_type: str = "local",
                max_results: int = 10,
                local_weight: float = 0.6,
                global_weight: float = 0.4,
                community_level: Optional[int] = None
            ) -> AsyncGenerator[Dict[str, Any], None]:
                """Stream search results as they become available"""
                self._log_tool_call("stream_search_results", query=query, search_type=search_type)

                try:
                    if search_type == "local":
                        async for result in self._stream_local_search(query, max_results, community_level=community_level):
                            yield result
                    elif search_type == "global":
                        async for result in self._stream_global_search(query, community_level=community_level):
                            yield result
                    elif search_type == "hybrid":
                        async for result in self._stream_hybrid_search(query, max_results, local_weight=local_weight, global_weight=global_weight):
                            yield result
                    else:
                        yield self._handle_tool_error("stream_search_results", ValueError(f"Unknown search type: {search_type}"))

                except Exception as e:
                    yield self._handle_tool_error("stream_search_results", e)
            
            @mcp_app.tool()
            async def track_search_progress(query: str) -> AsyncGenerator[Dict[str, Any], None]:
                """Track and stream search progress updates"""
                self._log_tool_call("track_search_progress", query=query)
                
                try:
                    stages = [
                        ("query_analysis", "Analyzing query structure and intent"),
                        ("vector_embedding", "Creating vector embeddings"),
                        ("similarity_search", "Performing similarity search"),
                        ("graph_traversal", "Traversing knowledge graph"),
                        ("result_ranking", "Ranking and scoring results"),
                        ("response_synthesis", "Synthesizing final response")
                    ]
                    
                    for i, (stage, description) in enumerate(stages):
                        progress = ((i + 1) / len(stages)) * 100
                        
                        yield {
                            "query": query,
                            "stage": stage,
                            "description": description,
                            "progress_percent": progress,
                            "estimated_remaining": (len(stages) - i - 1) * 0.5,
                            "current_operation": description,
                            "timestamp": time.time()
                        }
                        
                        await asyncio.sleep(0.5)  # Simulate processing time
                        
                except Exception as e:
                    yield self._handle_tool_error("track_search_progress", e)
        
        @mcp_app.tool()
        async def get_search_suggestions(query: str, max_suggestions: int = 5) -> Dict[str, Any]:
            """Get search suggestions based on query"""
            self._log_tool_call("get_search_suggestions", query=query)
            
            try:
                # Generate suggestions based on query
                suggestions = []
                query_words = query.lower().split()
                
                # Related term suggestions
                for word in query_words[:3]:  # Use first 3 words
                    suggestions.extend([
                        f"{word} applications",
                        f"{word} benefits",
                        f"{word} challenges",
                        f"how {word} works",
                        f"{word} vs alternatives"
                    ])
                
                # Remove duplicates and limit
                unique_suggestions = list(dict.fromkeys(suggestions))[:max_suggestions]
                
                return {
                    "original_query": query,
                    "suggestions": unique_suggestions,
                    "suggestion_count": len(unique_suggestions),
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("get_search_suggestions", e)
        
        # Track registered tools
        tools = {
            "local_search": local_search,
            "global_search": global_search,
            "hybrid_search": hybrid_search,
            "get_search_suggestions": get_search_suggestions
        }
        
        if self.enable_streaming:
            tools.update({
                "stream_search_results": stream_search_results,
                "track_search_progress": track_search_progress
            })
        
        self.registered_tools.update(tools)
        
        self.logger.info(f"Registered {len(self.registered_tools)} search tools")
    
    async def _stream_local_search(self, query: str, max_results: int, community_level: Optional[int] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream local search results"""
        for i in range(min(max_results, 5)):
            result = {
                "result_id": f"local_result_{i}",
                "content": f"Local search result {i} for: {query}",
                "relevance_score": 1.0 - (i * 0.1),
                "source": f"document_{i}.pdf",
                "search_type": "local",
                "timestamp": time.time(),
                "metadata": {
                    "community_id": f"community_{i % 3}",
                    "community_level": community_level,
                    "document_type": "pdf"
                }
            }
            yield result
            await asyncio.sleep(0.1)  # Simulate processing time

    async def _stream_global_search(self, query: str, community_level: Optional[int] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream global search results"""
        for i in range(3):
            result = {
                "result_id": f"global_community_{i}",
                "content": f"Global community {i} analysis for: {query}",
                "relevance_score": 0.9 - (i * 0.1),
                "search_type": "global",
                "community_id": f"community_{i}",
                "timestamp": time.time(),
                "metadata": {
                    "community_size": 50 - (i * 10),
                    "entity_count": 20 - (i * 5),
                    "community_level": community_level or 2
                }
            }
            yield result
            await asyncio.sleep(0.2)  # Simulate processing time

    async def _stream_hybrid_search(self, query: str, max_results: int, local_weight: float = 0.6, global_weight: float = 0.4) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream hybrid search results"""
        # Stream local results first
        local_count = 0
        async for result in self._stream_local_search(query, max_results // 2):
            result["weighted_score"] = result["relevance_score"] * local_weight
            result["source_type"] = "local"
            yield result
            local_count += 1

        # Stream global results
        global_count = 0
        async for result in self._stream_global_search(query):
            result["weighted_score"] = result["relevance_score"] * global_weight
            result["source_type"] = "global"
            yield result
            global_count += 1

            if local_count + global_count >= max_results:
                break
    
    def _update_average_response_time(self, execution_time: float):
        """Update average response time metric"""
        total_searches = self.search_metrics["total_searches"]
        current_avg = self.search_metrics["average_response_time"]
        
        # Calculate new average
        new_avg = ((current_avg * (total_searches - 1)) + execution_time) / total_searches
        self.search_metrics["average_response_time"] = new_avg
    
    def get_search_metrics(self) -> Dict[str, Any]:
        """Get search performance metrics"""
        return self.search_metrics.copy()
    
    def reset_search_metrics(self):
        """Reset search metrics"""
        self.search_metrics = {
            "total_searches": 0,
            "local_searches": 0,
            "global_searches": 0,
            "hybrid_searches": 0,
            "average_response_time": 0.0,
            "cache_hit_rate": 0.0
        }
        self.logger.info("Search metrics reset")