"""
Knowledge Graph Tools for MCP Integration

This module provides MCP tools for knowledge graph exploration,
including interactive graph traversal, node expansion, and path finding.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastmcp import FastMCP

from .base import BaseToolManager


class KnowledgeGraphTools(BaseToolManager):
    """
    Knowledge Graph Tool Manager
    
    Provides tools for interactive knowledge graph exploration,
    including node traversal, relationship discovery, and path finding.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Knowledge Graph Tools
        
        Args:
            config: Knowledge graph tools configuration
        """
        super().__init__(config)
        
        # Graph exploration configuration
        self.max_traversal_depth = config.get("max_traversal_depth", 5)
        self.max_nodes_per_query = config.get("max_nodes_per_query", 100)
        self.enable_streaming = config.get("enable_streaming", True)
        
        # Graph metrics
        self.graph_metrics = {
            "total_explorations": 0,
            "nodes_explored": 0,
            "relationships_discovered": 0,
            "paths_found": 0,
            "average_exploration_time": 0.0
        }
        
        self.logger.info(f"Knowledge graph tools configured: max_depth={self.max_traversal_depth}")
    
    def register_tools(self, mcp_app: FastMCP):
        """Register knowledge graph tools with MCP app"""
        if not self.enabled:
            return
        
        if self.enable_streaming:
            @mcp_app.tool()
            async def explore_knowledge_graph(
                start_node: str,
                max_depth: int = 3,
                max_nodes: int = 50,
                relationship_types: Optional[List[str]] = None
            ) -> AsyncGenerator[Dict[str, Any], None]:
                """Stream knowledge graph exploration results"""
                self._log_tool_call("explore_knowledge_graph", start_node=start_node, max_depth=max_depth)
                
                try:
                    start_time = time.time()
                    nodes_explored = 0
                    relationships_found = 0
                    
                    # Initialize exploration
                    yield {
                        "event_type": "exploration_started",
                        "start_node": start_node,
                        "max_depth": max_depth,
                        "max_nodes": max_nodes,
                        "timestamp": time.time()
                    }
                    
                    # Simulate graph traversal
                    nodes_to_explore = [(start_node, 0)]
                    explored_nodes = set()
                    
                    while nodes_to_explore and nodes_explored < max_nodes:
                        current_node, depth = nodes_to_explore.pop(0)
                        
                        if current_node in explored_nodes or depth > max_depth:
                            continue
                        
                        explored_nodes.add(current_node)
                        nodes_explored += 1
                        
                        # Yield current node
                        node_data = {
                            "element_type": "node",
                            "node_id": current_node,
                            "depth": depth,
                            "properties": {
                                "name": current_node,
                                "type": "concept",
                                "description": f"Description of {current_node}",
                                "degree": 5 - depth,  # Simulate decreasing connectivity
                                "community_id": f"community_{hash(current_node) % 5}"
                            },
                            "timestamp": time.time()
                        }
                        yield node_data
                        
                        # Add connected nodes if within depth limit
                        if depth < max_depth:
                            connected_nodes = [f"{current_node}_child_{i}" for i in range(min(3, max_nodes - nodes_explored))]
                            
                            for connected_node in connected_nodes:
                                nodes_to_explore.append((connected_node, depth + 1))
                                relationships_found += 1
                                
                                # Yield relationship
                                relationship_data = {
                                    "element_type": "relationship",
                                    "source_node": current_node,
                                    "target_node": connected_node,
                                    "relationship_type": self._get_relationship_type(current_node, connected_node, relationship_types),
                                    "properties": {
                                        "strength": max(0.5, 1.0 - (depth * 0.2)),
                                        "confidence": 0.8 + (depth * 0.05),
                                        "discovered_at": time.time()
                                    },
                                    "timestamp": time.time()
                                }
                                yield relationship_data
                        
                        await asyncio.sleep(0.1)  # Simulate processing time
                    
                    # Final summary
                    exploration_time = time.time() - start_time
                    self._update_graph_metrics(nodes_explored, relationships_found, exploration_time)
                    
                    yield {
                        "event_type": "exploration_completed",
                        "start_node": start_node,
                        "nodes_explored": nodes_explored,
                        "relationships_found": relationships_found,
                        "exploration_time": exploration_time,
                        "timestamp": time.time()
                    }
                    
                except Exception as e:
                    yield self._handle_tool_error("explore_knowledge_graph", e)
            
            @mcp_app.tool()
            async def expand_node(
                node_id: str,
                expansion_type: str = "neighbors",
                relationship_types: Optional[List[str]] = None,
                max_expansions: int = 10
            ) -> AsyncGenerator[Dict[str, Any], None]:
                """Interactively expand a specific node"""
                self._log_tool_call("expand_node", node_id=node_id, expansion_type=expansion_type)
                
                try:
                    # Yield initial node info
                    yield {
                        "event_type": "expansion_started",
                        "node_id": node_id,
                        "expansion_type": expansion_type,
                        "timestamp": time.time()
                    }
                    
                    if expansion_type == "neighbors":
                        # Find neighboring nodes
                        for i in range(min(max_expansions, 5)):
                            neighbor_id = f"{node_id}_neighbor_{i}"
                            
                            # Yield neighbor node
                            yield {
                                "element_type": "node",
                                "node_id": neighbor_id,
                                "expansion_source": node_id,
                                "properties": {
                                    "name": neighbor_id,
                                    "type": "related_concept",
                                    "relevance": 0.9 - (i * 0.1)
                                },
                                "timestamp": time.time()
                            }
                            
                            # Yield relationship
                            yield {
                                "element_type": "relationship",
                                "source_node": node_id,
                                "target_node": neighbor_id,
                                "relationship_type": self._get_relationship_type(node_id, neighbor_id, relationship_types),
                                "properties": {"strength": 0.8 - (i * 0.1)},
                                "timestamp": time.time()
                            }
                            
                            await asyncio.sleep(0.1)
                    
                    elif expansion_type == "hierarchy":
                        # Expand hierarchical relationships
                        # Parent nodes
                        parent_id = f"{node_id}_parent"
                        yield {
                            "element_type": "node",
                            "node_id": parent_id,
                            "expansion_source": node_id,
                            "hierarchy_level": "parent",
                            "properties": {"name": parent_id, "type": "parent_concept"},
                            "timestamp": time.time()
                        }
                        
                        # Child nodes
                        for i in range(3):
                            child_id = f"{node_id}_child_{i}"
                            yield {
                                "element_type": "node",
                                "node_id": child_id,
                                "expansion_source": node_id,
                                "hierarchy_level": "child",
                                "properties": {"name": child_id, "type": "child_concept"},
                                "timestamp": time.time()
                            }
                            await asyncio.sleep(0.1)
                    
                    yield {
                        "event_type": "expansion_completed",
                        "node_id": node_id,
                        "expansion_type": expansion_type,
                        "timestamp": time.time()
                    }
                    
                except Exception as e:
                    yield self._handle_tool_error("expand_node", e)
            
            @mcp_app.tool()
            async def find_graph_paths(
                start_node: str,
                end_node: str,
                max_path_length: int = 5,
                algorithm: str = "shortest_path",
                max_paths: int = 5
            ) -> AsyncGenerator[Dict[str, Any], None]:
                """Find paths between nodes in the knowledge graph"""
                self._log_tool_call("find_graph_paths", start_node=start_node, end_node=end_node)
                
                try:
                    yield {
                        "event_type": "path_search_started",
                        "start_node": start_node,
                        "end_node": end_node,
                        "algorithm": algorithm,
                        "timestamp": time.time()
                    }
                    
                    # Simulate path finding
                    for path_id in range(min(max_paths, 3)):
                        # Generate a path
                        path_length = min(max_path_length, 3 + path_id)
                        nodes = [start_node]
                        relationships = []
                        
                        # Generate intermediate nodes
                        for i in range(path_length - 1):
                            if i == path_length - 2:
                                next_node = end_node
                            else:
                                next_node = f"intermediate_{path_id}_{i}"
                            
                            nodes.append(next_node)
                            relationships.append({
                                "source": nodes[i],
                                "target": next_node,
                                "type": "CONNECTED_TO",
                                "weight": 1.0 - (i * 0.1)
                            })
                        
                        path_score = 1.0 - (path_id * 0.2)  # Decreasing score for alternative paths
                        
                        yield {
                            "element_type": "path",
                            "path_id": f"path_{path_id}",
                            "start_node": start_node,
                            "end_node": end_node,
                            "nodes": nodes,
                            "relationships": relationships,
                            "path_length": path_length,
                            "path_score": path_score,
                            "algorithm": algorithm,
                            "timestamp": time.time()
                        }
                        
                        await asyncio.sleep(0.2)
                    
                    yield {
                        "event_type": "path_search_completed",
                        "start_node": start_node,
                        "end_node": end_node,
                        "paths_found": min(max_paths, 3),
                        "timestamp": time.time()
                    }
                    
                except Exception as e:
                    yield self._handle_tool_error("find_graph_paths", e)
        
        @mcp_app.tool()
        async def get_node_details(node_id: str) -> Dict[str, Any]:
            """Get detailed information about a specific node"""
            self._log_tool_call("get_node_details", node_id=node_id)
            
            try:
                # Simulate node details retrieval
                node_details = {
                    "node_id": node_id,
                    "properties": {
                        "name": node_id,
                        "type": "concept",
                        "description": f"Detailed description of {node_id}",
                        "aliases": [f"{node_id}_alias_{i}" for i in range(2)],
                        "categories": ["category_1", "category_2"],
                        "confidence": 0.9,
                        "source_documents": [f"doc_{i}.pdf" for i in range(3)]
                    },
                    "statistics": {
                        "degree": 8,
                        "in_degree": 5,
                        "out_degree": 3,
                        "betweenness_centrality": 0.15,
                        "pagerank": 0.002,
                        "community_id": f"community_{hash(node_id) % 5}"
                    },
                    "relationships": {
                        "total_count": 8,
                        "by_type": {
                            "RELATED_TO": 4,
                            "PART_OF": 2,
                            "USED_IN": 2
                        }
                    },
                    "timestamp": time.time()
                }
                
                return node_details
                
            except Exception as e:
                return self._handle_tool_error("get_node_details", e)
        
        @mcp_app.tool()
        async def search_graph_nodes(
            query: str,
            node_types: Optional[List[str]] = None,
            max_results: int = 20
        ) -> Dict[str, Any]:
            """Search for nodes in the knowledge graph"""
            self._log_tool_call("search_graph_nodes", query=query, node_types=node_types)
            
            try:
                # Simulate node search
                results = []
                query_words = query.lower().split()
                
                for i in range(min(max_results, 10)):
                    node_id = f"search_result_{i}_{query_words[0] if query_words else 'node'}"
                    
                    # Calculate relevance score
                    relevance = 1.0 - (i * 0.08)
                    
                    results.append({
                        "node_id": node_id,
                        "name": node_id.replace("_", " ").title(),
                        "type": node_types[i % len(node_types)] if node_types else "concept",
                        "relevance_score": relevance,
                        "description": f"Node matching query: {query}",
                        "properties": {
                            "degree": 10 - i,
                            "community_id": f"community_{i % 3}"
                        }
                    })
                
                return {
                    "query": query,
                    "results": results,
                    "total_results": len(results),
                    "node_types_filter": node_types,
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("search_graph_nodes", e)
        
        @mcp_app.tool()
        async def get_graph_statistics() -> Dict[str, Any]:
            """Get overall knowledge graph statistics"""
            self._log_tool_call("get_graph_statistics")
            
            try:
                # Simulate graph statistics
                stats = {
                    "nodes": {
                        "total_count": 15420,
                        "by_type": {
                            "concept": 8500,
                            "entity": 4200,
                            "document": 2720
                        }
                    },
                    "relationships": {
                        "total_count": 45680,
                        "by_type": {
                            "RELATED_TO": 18500,
                            "PART_OF": 12200,
                            "USED_IN": 8900,
                            "MENTIONS": 6080
                        }
                    },
                    "communities": {
                        "total_count": 125,
                        "average_size": 123.36,
                        "largest_community_size": 850,
                        "smallest_community_size": 12
                    },
                    "connectivity": {
                        "average_degree": 5.92,
                        "density": 0.0004,
                        "clustering_coefficient": 0.34,
                        "diameter": 8
                    },
                    "performance_metrics": self.graph_metrics,
                    "last_updated": time.time(),
                    "timestamp": time.time()
                }
                
                return stats
                
            except Exception as e:
                return self._handle_tool_error("get_graph_statistics", e)
        
        # Track registered tools
        tools = {
            "get_node_details": get_node_details,
            "search_graph_nodes": search_graph_nodes,
            "get_graph_statistics": get_graph_statistics
        }
        
        if self.enable_streaming:
            tools.update({
                "explore_knowledge_graph": explore_knowledge_graph,
                "expand_node": expand_node,
                "find_graph_paths": find_graph_paths
            })
        
        self.registered_tools.update(tools)
        
        self.logger.info(f"Registered {len(self.registered_tools)} knowledge graph tools")
    
    def _get_relationship_type(self, source: str, target: str, allowed_types: Optional[List[str]] = None) -> str:
        """Determine relationship type between nodes"""
        default_types = ["RELATED_TO", "PART_OF", "USED_IN", "MENTIONS", "CONNECTED_TO"]
        
        if allowed_types:
            available_types = [t for t in allowed_types if t in default_types]
        else:
            available_types = default_types
        
        # Simple heuristic based on node names
        if "child" in target:
            return "PART_OF"
        elif "parent" in target:
            return "PART_OF"
        elif "neighbor" in target:
            return "RELATED_TO"
        else:
            return available_types[hash(source + target) % len(available_types)]
    
    def _update_graph_metrics(self, nodes_explored: int, relationships_found: int, exploration_time: float):
        """Update graph exploration metrics"""
        self.graph_metrics["total_explorations"] += 1
        self.graph_metrics["nodes_explored"] += nodes_explored
        self.graph_metrics["relationships_discovered"] += relationships_found
        
        # Update average exploration time
        total_explorations = self.graph_metrics["total_explorations"]
        current_avg = self.graph_metrics["average_exploration_time"]
        new_avg = ((current_avg * (total_explorations - 1)) + exploration_time) / total_explorations
        self.graph_metrics["average_exploration_time"] = new_avg
    
    def get_graph_metrics(self) -> Dict[str, Any]:
        """Get graph exploration metrics"""
        return self.graph_metrics.copy()