"""
Cache Tools for MCP Integration

This module provides MCP tools for cache management,
including distributed caching, cache invalidation, and cache analytics.
"""

import asyncio
import time
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastmcp import FastMCP

from .base import BaseToolManager


class CacheTools(BaseToolManager):
    """
    Cache Tool Manager
    
    Provides tools for cache operations including get, set, invalidate,
    and distributed cache management with real-time invalidation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Cache Tools
        
        Args:
            config: Cache tools configuration
        """
        super().__init__(config)
        
        # Cache configuration
        self.enable_distributed_cache = config.get("enable_distributed_cache", True)
        self.cache_invalidation_timeout = config.get("cache_invalidation_timeout", 5)
        self.cache_sync_interval = config.get("cache_sync_interval", 10)
        
        # Mock cache storage (in production, this would be Redis/Memcached)
        self.cache_storage = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "invalidations": 0,
            "total_size": 0
        }
        
        # Cache invalidation subscribers
        self.invalidation_subscribers = set()
        
        # Background tasks
        self.background_tasks = []
        
        self.logger.info(f"Cache tools configured: distributed={self.enable_distributed_cache}")
    
    def register_tools(self, mcp_app: FastMCP):
        """Register cache tools with MCP app"""
        if not self.enabled:
            return
        
        @mcp_app.tool()
        async def get_cache_entry(
            cache_key: str,
            cache_type: str = "query_results"
        ) -> Dict[str, Any]:
            """Get entry from cache"""
            self._log_tool_call("get_cache_entry", cache_key=cache_key, cache_type=cache_type)
            
            try:
                full_key = f"{cache_type}:{cache_key}"
                
                if full_key in self.cache_storage:
                    entry = self.cache_storage[full_key]
                    
                    # Check TTL
                    if entry.get("ttl") and time.time() > entry["created_at"] + entry["ttl"]:
                        # Expired, remove from cache
                        del self.cache_storage[full_key]
                        self.cache_stats["misses"] += 1
                        return {
                            "cache_key": cache_key,
                            "cache_type": cache_type,
                            "found": False,
                            "value": None,
                            "metadata": None,
                            "reason": "expired"
                        }
                    
                    # Update hit count and stats
                    entry["hit_count"] += 1
                    entry["last_accessed"] = time.time()
                    self.cache_stats["hits"] += 1
                    
                    return {
                        "cache_key": cache_key,
                        "cache_type": cache_type,
                        "found": True,
                        "value": entry["value"],
                        "metadata": entry["metadata"],
                        "hit_count": entry["hit_count"],
                        "created_at": entry["created_at"],
                        "last_accessed": entry["last_accessed"]
                    }
                else:
                    self.cache_stats["misses"] += 1
                    return {
                        "cache_key": cache_key,
                        "cache_type": cache_type,
                        "found": False,
                        "value": None,
                        "metadata": None,
                        "reason": "not_found"
                    }
                    
            except Exception as e:
                return self._handle_tool_error("get_cache_entry", e)
        
        @mcp_app.tool()
        async def set_cache_entry(
            cache_key: str,
            value: Any,
            cache_type: str = "query_results",
            ttl: Optional[int] = None,
            metadata: Optional[Dict[str, Any]] = None
        ) -> Dict[str, Any]:
            """Set entry in cache"""
            self._log_tool_call("set_cache_entry", cache_key=cache_key, cache_type=cache_type)
            
            try:
                full_key = f"{cache_type}:{cache_key}"
                
                entry = {
                    "value": value,
                    "metadata": metadata or {},
                    "ttl": ttl,
                    "created_at": time.time(),
                    "last_accessed": time.time(),
                    "hit_count": 0,
                    "size_bytes": len(json.dumps(value)) if isinstance(value, (dict, list)) else len(str(value))
                }
                
                # Update storage
                old_size = 0
                if full_key in self.cache_storage:
                    old_size = self.cache_storage[full_key]["size_bytes"]
                
                self.cache_storage[full_key] = entry
                
                # Update stats
                self.cache_stats["sets"] += 1
                self.cache_stats["total_size"] += entry["size_bytes"] - old_size
                
                return {
                    "cache_key": cache_key,
                    "cache_type": cache_type,
                    "success": True,
                    "size_bytes": entry["size_bytes"],
                    "ttl": ttl,
                    "created_at": entry["created_at"]
                }
                
            except Exception as e:
                return self._handle_tool_error("set_cache_entry", e)
        
        @mcp_app.tool()
        async def invalidate_cache(
            cache_pattern: str,
            cache_type: Optional[str] = None
        ) -> Dict[str, Any]:
            """Invalidate cache entries matching pattern"""
            self._log_tool_call("invalidate_cache", cache_pattern=cache_pattern, cache_type=cache_type)
            
            try:
                invalidated_keys = []
                invalidated_size = 0
                
                for full_key in list(self.cache_storage.keys()):
                    # Check cache type filter
                    if cache_type and not full_key.startswith(f"{cache_type}:"):
                        continue
                    
                    # Extract cache key
                    cache_key = full_key.split(":", 1)[1] if ":" in full_key else full_key
                    
                    # Check pattern match
                    if cache_pattern in cache_key or cache_pattern == "*":
                        entry = self.cache_storage[full_key]
                        invalidated_size += entry["size_bytes"]
                        del self.cache_storage[full_key]
                        invalidated_keys.append(cache_key)
                
                # Update stats
                self.cache_stats["invalidations"] += len(invalidated_keys)
                self.cache_stats["total_size"] -= invalidated_size
                
                # Notify subscribers
                if self.enable_distributed_cache and invalidated_keys:
                    await self._broadcast_invalidation(cache_pattern, cache_type, invalidated_keys)
                
                return {
                    "cache_pattern": cache_pattern,
                    "cache_type": cache_type,
                    "invalidated_count": len(invalidated_keys),
                    "invalidated_keys": invalidated_keys,
                    "freed_bytes": invalidated_size,
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("invalidate_cache", e)
        
        @mcp_app.tool()
        async def get_cache_stats() -> Dict[str, Any]:
            """Get cache performance statistics"""
            self._log_tool_call("get_cache_stats")
            
            try:
                total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
                hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
                
                # Get cache type breakdown
                cache_types = {}
                for full_key in self.cache_storage.keys():
                    cache_type = full_key.split(":", 1)[0] if ":" in full_key else "default"
                    if cache_type not in cache_types:
                        cache_types[cache_type] = {"count": 0, "size": 0}
                    
                    cache_types[cache_type]["count"] += 1
                    cache_types[cache_type]["size"] += self.cache_storage[full_key]["size_bytes"]
                
                return {
                    "total_entries": len(self.cache_storage),
                    "cache_hits": self.cache_stats["hits"],
                    "cache_misses": self.cache_stats["misses"],
                    "cache_sets": self.cache_stats["sets"],
                    "cache_invalidations": self.cache_stats["invalidations"],
                    "hit_rate": hit_rate,
                    "total_size_bytes": self.cache_stats["total_size"],
                    "cache_types": cache_types,
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("get_cache_stats", e)
        
        if self.enable_distributed_cache:
            @mcp_app.tool()
            async def listen_cache_invalidations() -> AsyncGenerator[Dict[str, Any], None]:
                """Listen for cache invalidation events"""
                self._log_tool_call("listen_cache_invalidations")
                
                try:
                    subscriber_id = f"subscriber_{int(time.time() * 1000)}"
                    self.invalidation_subscribers.add(subscriber_id)
                    
                    try:
                        # Send initial status
                        yield {
                            "event_type": "subscription_started",
                            "subscriber_id": subscriber_id,
                            "timestamp": time.time()
                        }
                        
                        # Listen for invalidation events
                        while subscriber_id in self.invalidation_subscribers:
                            # In a real implementation, this would listen to Redis pub/sub or similar
                            # For now, we'll simulate periodic events
                            await asyncio.sleep(5)
                            
                            # Send keepalive
                            yield {
                                "event_type": "keepalive",
                                "subscriber_id": subscriber_id,
                                "timestamp": time.time(),
                                "cache_stats": await get_cache_stats()
                            }
                            
                    finally:
                        self.invalidation_subscribers.discard(subscriber_id)
                        
                except Exception as e:
                    yield self._handle_tool_error("listen_cache_invalidations", e)
            
            @mcp_app.tool()
            async def sync_distributed_cache() -> Dict[str, Any]:
                """Synchronize with distributed cache instances"""
                self._log_tool_call("sync_distributed_cache")
                
                try:
                    # Simulate distributed cache synchronization
                    sync_stats = {
                        "local_entries": len(self.cache_storage),
                        "remote_entries_discovered": 0,
                        "conflicts_resolved": 0,
                        "sync_duration": 0.0
                    }
                    
                    start_time = time.time()
                    
                    # Simulate sync process
                    await asyncio.sleep(0.5)
                    
                    sync_stats["sync_duration"] = time.time() - start_time
                    sync_stats["timestamp"] = time.time()
                    sync_stats["status"] = "completed"
                    
                    return sync_stats
                    
                except Exception as e:
                    return self._handle_tool_error("sync_distributed_cache", e)
        
        @mcp_app.tool()
        async def clear_cache(
            cache_type: Optional[str] = None,
            confirm: bool = False
        ) -> Dict[str, Any]:
            """Clear cache entries (requires confirmation)"""
            self._log_tool_call("clear_cache", cache_type=cache_type, confirm=confirm)
            
            try:
                if not confirm:
                    return {
                        "error": True,
                        "message": "Cache clear requires confirmation (set confirm=True)",
                        "cache_type": cache_type
                    }
                
                cleared_count = 0
                cleared_size = 0
                
                if cache_type:
                    # Clear specific cache type
                    keys_to_remove = [k for k in self.cache_storage.keys() if k.startswith(f"{cache_type}:")]
                else:
                    # Clear all cache
                    keys_to_remove = list(self.cache_storage.keys())
                
                for key in keys_to_remove:
                    entry = self.cache_storage[key]
                    cleared_size += entry["size_bytes"]
                    del self.cache_storage[key]
                    cleared_count += 1
                
                # Update stats
                self.cache_stats["total_size"] -= cleared_size
                
                return {
                    "cache_type": cache_type or "all",
                    "cleared_count": cleared_count,
                    "freed_bytes": cleared_size,
                    "remaining_entries": len(self.cache_storage),
                    "timestamp": time.time()
                }
                
            except Exception as e:
                return self._handle_tool_error("clear_cache", e)
        
        # Track registered tools
        tools = {
            "get_cache_entry": get_cache_entry,
            "set_cache_entry": set_cache_entry,
            "invalidate_cache": invalidate_cache,
            "get_cache_stats": get_cache_stats,
            "clear_cache": clear_cache
        }
        
        if self.enable_distributed_cache:
            tools.update({
                "listen_cache_invalidations": listen_cache_invalidations,
                "sync_distributed_cache": sync_distributed_cache
            })
        
        self.registered_tools.update(tools)
        
        self.logger.info(f"Registered {len(self.registered_tools)} cache tools")
    
    async def _broadcast_invalidation(self, pattern: str, cache_type: Optional[str], invalidated_keys: List[str]):
        """Broadcast cache invalidation to subscribers"""
        invalidation_event = {
            "event_type": "cache_invalidation",
            "cache_pattern": pattern,
            "cache_type": cache_type,
            "invalidated_keys": invalidated_keys,
            "invalidation_reason": "manual",
            "timestamp": time.time()
        }
        
        # In a real implementation, this would publish to Redis pub/sub or similar
        self.logger.debug(f"Broadcasting invalidation: {invalidation_event}")
    
    async def start_cache_sync(self):
        """Start background cache synchronization"""
        if not self.enable_distributed_cache:
            return
        
        async def sync_task():
            while True:
                try:
                    await asyncio.sleep(self.cache_sync_interval)
                    # Perform cache synchronization
                    self.logger.debug("Performing cache synchronization")
                    
                except Exception as e:
                    self.logger.error(f"Error in cache sync: {e}")
                    await asyncio.sleep(self.cache_sync_interval)
        
        task = asyncio.create_task(sync_task())
        self.background_tasks.append(task)
        self.logger.info("Started cache synchronization task")
    
    async def stop(self):
        """Stop background tasks"""
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.background_tasks.clear()
        self.logger.info("Stopped cache tools background tasks")
    
    def get_cache_metrics(self) -> Dict[str, Any]:
        """Get cache performance metrics"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            "hit_rate": hit_rate,
            "total_requests": total_requests,
            "average_entry_size": self.cache_stats["total_size"] / len(self.cache_storage) if self.cache_storage else 0
        }