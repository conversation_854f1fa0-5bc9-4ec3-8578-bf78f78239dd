"""
Performance Monitoring Tools for MCP Integration

This module provides MCP tools for performance monitoring,
including real-time metrics, alerts, and system health monitoring.
"""

import asyncio
import time
import psutil
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastmcp import FastMCP

from .base import BaseToolManager


class PerformanceMonitoringTools(BaseToolManager):
    """
    Performance Monitoring Tool Manager
    
    Provides tools for monitoring system performance, generating alerts,
    and streaming real-time metrics.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Performance Monitoring Tools
        
        Args:
            config: Performance monitoring configuration
        """
        super().__init__(config)
        
        # Monitoring configuration
        self.enable_performance_monitoring = config.get("enable_performance_monitoring", True)
        self.metrics_collection_interval = config.get("metrics_collection_interval", 1)
        self.performance_alert_thresholds = config.get("performance_alert_thresholds", {})
        
        # Performance data storage
        self.metrics_history = []
        self.max_history_size = 1000
        self.alerts_generated = []
        
        # Background tasks
        self.background_tasks = []
        self.monitoring_active = False
        
        # Metrics subscribers
        self.metrics_subscribers = set()
        
        self.logger.info(f"Performance monitoring configured: enabled={self.enable_performance_monitoring}")
    
    def register_tools(self, mcp_app: FastMCP):
        """Register performance monitoring tools with MCP app"""
        if not self.enabled:
            return
        
        @mcp_app.tool()
        async def get_system_metrics() -> Dict[str, Any]:
            """Get current system performance metrics"""
            self._log_tool_call("get_system_metrics")
            
            try:
                metrics = self._collect_system_metrics()
                return {
                    "timestamp": time.time(),
                    "metrics": metrics,
                    "status": "healthy" if self._assess_system_health(metrics) else "warning"
                }
                
            except Exception as e:
                return self._handle_tool_error("get_system_metrics", e)
        
        @mcp_app.tool()
        async def get_application_metrics() -> Dict[str, Any]:
            """Get application-specific performance metrics"""
            self._log_tool_call("get_application_metrics")
            
            try:
                app_metrics = {
                    "mcp_server": {
                        "active_connections": len(self.metrics_subscribers),
                        "total_tool_calls": self._get_total_tool_calls(),
                        "average_response_time": self._get_average_response_time(),
                        "error_rate": self._get_error_rate()
                    },
                    "cache": {
                        "hit_rate": 0.85,  # Would come from cache manager
                        "total_entries": 1250,
                        "memory_usage_mb": 45.2
                    },
                    "search": {
                        "total_searches": 342,
                        "average_search_time": 1.2,
                        "search_success_rate": 0.96
                    },
                    "agents": {
                        "active_agents": 3,
                        "coordination_sessions": 2,
                        "average_agent_response_time": 2.1
                    }
                }
                
                return {
                    "timestamp": time.time(),
                    "application_metrics": app_metrics,
                    "health_status": self._assess_application_health(app_metrics)
                }
                
            except Exception as e:
                return self._handle_tool_error("get_application_metrics", e)
        
        if self.enable_performance_monitoring:
            @mcp_app.tool()
            async def stream_performance_metrics(
                metric_types: Optional[List[str]] = None,
                interval_seconds: float = 1.0
            ) -> AsyncGenerator[Dict[str, Any], None]:
                """Stream real-time performance metrics"""
                self._log_tool_call("stream_performance_metrics", metric_types=metric_types)
                
                try:
                    subscriber_id = f"metrics_subscriber_{int(time.time() * 1000)}"
                    self.metrics_subscribers.add(subscriber_id)
                    
                    try:
                        # Send initial status
                        yield {
                            "event_type": "metrics_stream_started",
                            "subscriber_id": subscriber_id,
                            "metric_types": metric_types or ["all"],
                            "interval_seconds": interval_seconds,
                            "timestamp": time.time()
                        }
                        
                        # Stream metrics
                        while subscriber_id in self.metrics_subscribers:
                            metrics_data = {
                                "event_type": "metrics_update",
                                "subscriber_id": subscriber_id,
                                "timestamp": time.time()
                            }
                            
                            # Collect requested metrics
                            if not metric_types or "system" in metric_types:
                                metrics_data["system_metrics"] = self._collect_system_metrics()
                            
                            if not metric_types or "application" in metric_types:
                                metrics_data["application_metrics"] = (await get_application_metrics())["application_metrics"]
                            
                            if not metric_types or "network" in metric_types:
                                metrics_data["network_metrics"] = self._collect_network_metrics()
                            
                            # Check for alerts
                            alerts = self._check_performance_alerts(metrics_data)
                            if alerts:
                                metrics_data["alerts"] = alerts
                            
                            yield metrics_data
                            await asyncio.sleep(interval_seconds)
                            
                    finally:
                        self.metrics_subscribers.discard(subscriber_id)
                        
                except Exception as e:
                    yield self._handle_tool_error("stream_performance_metrics", e)
            
            @mcp_app.tool()
            async def get_performance_alerts() -> Dict[str, Any]:
                """Get current performance alerts"""
                self._log_tool_call("get_performance_alerts")
                
                try:
                    # Get recent alerts (last 24 hours)
                    current_time = time.time()
                    recent_alerts = [
                        alert for alert in self.alerts_generated
                        if current_time - alert["timestamp"] < 86400  # 24 hours
                    ]
                    
                    # Categorize alerts by severity
                    alerts_by_severity = {
                        "critical": [a for a in recent_alerts if a["severity"] == "critical"],
                        "warning": [a for a in recent_alerts if a["severity"] == "warning"],
                        "info": [a for a in recent_alerts if a["severity"] == "info"]
                    }
                    
                    return {
                        "total_alerts": len(recent_alerts),
                        "alerts_by_severity": alerts_by_severity,
                        "active_alerts": [a for a in recent_alerts if a.get("resolved", False) is False],
                        "timestamp": time.time()
                    }
                    
                except Exception as e:
                    return self._handle_tool_error("get_performance_alerts", e)
            
            @mcp_app.tool()
            async def get_metrics_history(
                metric_name: str,
                time_range_minutes: int = 60,
                aggregation: str = "raw"
            ) -> Dict[str, Any]:
                """Get historical metrics data"""
                self._log_tool_call("get_metrics_history", metric_name=metric_name, time_range_minutes=time_range_minutes)
                
                try:
                    current_time = time.time()
                    start_time = current_time - (time_range_minutes * 60)
                    
                    # Filter metrics by time range
                    relevant_metrics = [
                        m for m in self.metrics_history
                        if m["timestamp"] >= start_time and metric_name in m.get("metrics", {})
                    ]
                    
                    if not relevant_metrics:
                        return {
                            "metric_name": metric_name,
                            "time_range_minutes": time_range_minutes,
                            "data_points": [],
                            "message": "No data available for the specified time range"
                        }
                    
                    # Extract metric values
                    data_points = []
                    for metric in relevant_metrics:
                        data_points.append({
                            "timestamp": metric["timestamp"],
                            "value": metric["metrics"].get(metric_name, 0)
                        })
                    
                    # Apply aggregation if requested
                    if aggregation != "raw" and len(data_points) > 100:
                        data_points = self._aggregate_metrics(data_points, aggregation)
                    
                    # Calculate statistics
                    values = [dp["value"] for dp in data_points]
                    statistics = {
                        "min": min(values) if values else 0,
                        "max": max(values) if values else 0,
                        "avg": sum(values) / len(values) if values else 0,
                        "count": len(values)
                    }
                    
                    return {
                        "metric_name": metric_name,
                        "time_range_minutes": time_range_minutes,
                        "aggregation": aggregation,
                        "data_points": data_points,
                        "statistics": statistics,
                        "timestamp": time.time()
                    }
                    
                except Exception as e:
                    return self._handle_tool_error("get_metrics_history", e)
        
        @mcp_app.tool()
        async def generate_performance_report() -> Dict[str, Any]:
            """Generate comprehensive performance report"""
            self._log_tool_call("generate_performance_report")
            
            try:
                # Collect current metrics
                system_metrics = self._collect_system_metrics()
                app_metrics = (await get_application_metrics())["application_metrics"]
                
                # Calculate trends (if history available)
                trends = self._calculate_performance_trends()
                
                # Generate recommendations
                recommendations = self._generate_performance_recommendations(system_metrics, app_metrics)
                
                report = {
                    "report_id": f"perf_report_{int(time.time())}",
                    "generated_at": time.time(),
                    "summary": {
                        "overall_health": self._assess_overall_health(system_metrics, app_metrics),
                        "critical_issues": len([a for a in self.alerts_generated if a["severity"] == "critical"]),
                        "performance_score": self._calculate_performance_score(system_metrics, app_metrics)
                    },
                    "current_metrics": {
                        "system": system_metrics,
                        "application": app_metrics
                    },
                    "trends": trends,
                    "recommendations": recommendations,
                    "alerts_summary": {
                        "total_alerts_24h": len([a for a in self.alerts_generated if time.time() - a["timestamp"] < 86400]),
                        "recent_critical": [a for a in self.alerts_generated if a["severity"] == "critical" and time.time() - a["timestamp"] < 3600]
                    }
                }
                
                return report
                
            except Exception as e:
                return self._handle_tool_error("generate_performance_report", e)
        
        # Track registered tools
        tools = {
            "get_system_metrics": get_system_metrics,
            "get_application_metrics": get_application_metrics,
            "generate_performance_report": generate_performance_report
        }
        
        if self.enable_performance_monitoring:
            tools.update({
                "stream_performance_metrics": stream_performance_metrics,
                "get_performance_alerts": get_performance_alerts,
                "get_metrics_history": get_metrics_history
            })
        
        self.registered_tools.update(tools)
        
        self.logger.info(f"Registered {len(self.registered_tools)} performance monitoring tools")
    
    def _collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system-level performance metrics"""
        try:
            return {
                "cpu_usage_percent": psutil.cpu_percent(interval=0.1),
                "memory_usage_percent": psutil.virtual_memory().percent,
                "memory_available_gb": psutil.virtual_memory().available / (1024**3),
                "disk_usage_percent": psutil.disk_usage('/').percent,
                "disk_free_gb": psutil.disk_usage('/').free / (1024**3),
                "network_io": {
                    "bytes_sent": psutil.net_io_counters().bytes_sent,
                    "bytes_recv": psutil.net_io_counters().bytes_recv
                },
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0],
                "process_count": len(psutil.pids())
            }
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            return {}
    
    def _collect_network_metrics(self) -> Dict[str, Any]:
        """Collect network-specific metrics"""
        try:
            net_io = psutil.net_io_counters()
            return {
                "bytes_sent_per_sec": net_io.bytes_sent,  # Would calculate rate in real implementation
                "bytes_recv_per_sec": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv,
                "errors_in": net_io.errin,
                "errors_out": net_io.errout,
                "drops_in": net_io.dropin,
                "drops_out": net_io.dropout
            }
        except Exception as e:
            self.logger.error(f"Error collecting network metrics: {e}")
            return {}
    
    def _assess_system_health(self, metrics: Dict[str, Any]) -> bool:
        """Assess overall system health based on metrics"""
        if not metrics:
            return False
        
        # Check critical thresholds
        cpu_ok = metrics.get("cpu_usage_percent", 0) < 90
        memory_ok = metrics.get("memory_usage_percent", 0) < 90
        disk_ok = metrics.get("disk_usage_percent", 0) < 95
        
        return cpu_ok and memory_ok and disk_ok
    
    def _assess_application_health(self, app_metrics: Dict[str, Any]) -> str:
        """Assess application health"""
        try:
            # Check various application metrics
            cache_hit_rate = app_metrics.get("cache", {}).get("hit_rate", 0)
            search_success_rate = app_metrics.get("search", {}).get("search_success_rate", 0)
            error_rate = app_metrics.get("mcp_server", {}).get("error_rate", 0)
            
            if cache_hit_rate > 0.8 and search_success_rate > 0.9 and error_rate < 0.05:
                return "healthy"
            elif cache_hit_rate > 0.6 and search_success_rate > 0.8 and error_rate < 0.1:
                return "warning"
            else:
                return "critical"
                
        except Exception:
            return "unknown"
    
    def _check_performance_alerts(self, metrics_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check metrics against alert thresholds"""
        alerts = []
        current_time = time.time()
        
        # Check system metrics
        system_metrics = metrics_data.get("system_metrics", {})
        
        # CPU usage alert
        cpu_usage = system_metrics.get("cpu_usage_percent", 0)
        if cpu_usage > self.performance_alert_thresholds.get("cpu_critical", 90):
            alerts.append({
                "alert_id": f"cpu_critical_{int(current_time)}",
                "severity": "critical",
                "metric": "cpu_usage_percent",
                "value": cpu_usage,
                "threshold": self.performance_alert_thresholds.get("cpu_critical", 90),
                "message": f"CPU usage critically high: {cpu_usage:.1f}%",
                "timestamp": current_time
            })
        elif cpu_usage > self.performance_alert_thresholds.get("cpu_warning", 75):
            alerts.append({
                "alert_id": f"cpu_warning_{int(current_time)}",
                "severity": "warning",
                "metric": "cpu_usage_percent",
                "value": cpu_usage,
                "threshold": self.performance_alert_thresholds.get("cpu_warning", 75),
                "message": f"CPU usage high: {cpu_usage:.1f}%",
                "timestamp": current_time
            })
        
        # Memory usage alert
        memory_usage = system_metrics.get("memory_usage_percent", 0)
        if memory_usage > self.performance_alert_thresholds.get("memory_critical", 90):
            alerts.append({
                "alert_id": f"memory_critical_{int(current_time)}",
                "severity": "critical",
                "metric": "memory_usage_percent",
                "value": memory_usage,
                "threshold": self.performance_alert_thresholds.get("memory_critical", 90),
                "message": f"Memory usage critically high: {memory_usage:.1f}%",
                "timestamp": current_time
            })
        
        # Store alerts for history
        self.alerts_generated.extend(alerts)
        
        # Keep only recent alerts
        self.alerts_generated = [
            a for a in self.alerts_generated
            if current_time - a["timestamp"] < 86400  # Keep 24 hours
        ]
        
        return alerts
    
    def _get_total_tool_calls(self) -> int:
        """Get total number of tool calls (mock implementation)"""
        return 1250  # Would come from actual metrics
    
    def _get_average_response_time(self) -> float:
        """Get average response time (mock implementation)"""
        return 0.85  # Would come from actual metrics
    
    def _get_error_rate(self) -> float:
        """Get error rate (mock implementation)"""
        return 0.02  # Would come from actual metrics
    
    def _aggregate_metrics(self, data_points: List[Dict[str, Any]], aggregation: str) -> List[Dict[str, Any]]:
        """Aggregate metrics data"""
        if aggregation == "minute":
            # Group by minute and average
            grouped = {}
            for point in data_points:
                minute_key = int(point["timestamp"] // 60) * 60
                if minute_key not in grouped:
                    grouped[minute_key] = []
                grouped[minute_key].append(point["value"])
            
            return [
                {"timestamp": timestamp, "value": sum(values) / len(values)}
                for timestamp, values in sorted(grouped.items())
            ]
        
        return data_points  # Return raw data for unknown aggregation
    
    def _calculate_performance_trends(self) -> Dict[str, Any]:
        """Calculate performance trends from historical data"""
        if len(self.metrics_history) < 2:
            return {"message": "Insufficient data for trend analysis"}
        
        # Mock trend calculation
        return {
            "cpu_trend": "stable",
            "memory_trend": "increasing",
            "response_time_trend": "improving",
            "error_rate_trend": "stable"
        }
    
    def _generate_performance_recommendations(self, system_metrics: Dict[str, Any], app_metrics: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        # Check CPU usage
        cpu_usage = system_metrics.get("cpu_usage_percent", 0)
        if cpu_usage > 80:
            recommendations.append("Consider scaling up CPU resources or optimizing CPU-intensive operations")
        
        # Check memory usage
        memory_usage = system_metrics.get("memory_usage_percent", 0)
        if memory_usage > 80:
            recommendations.append("Consider increasing memory allocation or optimizing memory usage")
        
        # Check cache hit rate
        cache_hit_rate = app_metrics.get("cache", {}).get("hit_rate", 0)
        if cache_hit_rate < 0.7:
            recommendations.append("Improve cache hit rate by optimizing cache keys and TTL settings")
        
        # Check response times
        avg_response_time = app_metrics.get("mcp_server", {}).get("average_response_time", 0)
        if avg_response_time > 2.0:
            recommendations.append("Optimize response times by reviewing slow operations and adding caching")
        
        if not recommendations:
            recommendations.append("System performance is within acceptable ranges")
        
        return recommendations
    
    def _assess_overall_health(self, system_metrics: Dict[str, Any], app_metrics: Dict[str, Any]) -> str:
        """Assess overall system health"""
        system_healthy = self._assess_system_health(system_metrics)
        app_health = self._assess_application_health(app_metrics)
        
        if system_healthy and app_health == "healthy":
            return "excellent"
        elif system_healthy and app_health == "warning":
            return "good"
        elif system_healthy and app_health == "critical":
            return "degraded"
        else:
            return "critical"
    
    def _calculate_performance_score(self, system_metrics: Dict[str, Any], app_metrics: Dict[str, Any]) -> float:
        """Calculate overall performance score (0-100)"""
        score = 100.0
        
        # Deduct points for high resource usage
        cpu_usage = system_metrics.get("cpu_usage_percent", 0)
        memory_usage = system_metrics.get("memory_usage_percent", 0)
        
        score -= max(0, cpu_usage - 50) * 0.5  # Deduct for CPU usage over 50%
        score -= max(0, memory_usage - 50) * 0.5  # Deduct for memory usage over 50%
        
        # Deduct points for poor application metrics
        cache_hit_rate = app_metrics.get("cache", {}).get("hit_rate", 1.0)
        score -= (1.0 - cache_hit_rate) * 20  # Deduct up to 20 points for poor cache performance
        
        error_rate = app_metrics.get("mcp_server", {}).get("error_rate", 0)
        score -= error_rate * 100  # Deduct points for errors
        
        return max(0, min(100, score))
    
    async def start_monitoring(self):
        """Start background performance monitoring"""
        if not self.enable_performance_monitoring or self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        async def monitoring_task():
            while self.monitoring_active:
                try:
                    # Collect metrics
                    metrics = {
                        "timestamp": time.time(),
                        "metrics": self._collect_system_metrics()
                    }
                    
                    # Store in history
                    self.metrics_history.append(metrics)
                    
                    # Limit history size
                    if len(self.metrics_history) > self.max_history_size:
                        self.metrics_history = self.metrics_history[-self.max_history_size:]
                    
                    await asyncio.sleep(self.metrics_collection_interval)
                    
                except Exception as e:
                    self.logger.error(f"Error in monitoring task: {e}")
                    await asyncio.sleep(self.metrics_collection_interval)
        
        task = asyncio.create_task(monitoring_task())
        self.background_tasks.append(task)
        self.logger.info("Started performance monitoring task")
    
    async def stop(self):
        """Stop background monitoring tasks"""
        self.monitoring_active = False
        
        for task in self.background_tasks:
            task.cancel()
        
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.background_tasks.clear()
        self.logger.info("Stopped performance monitoring tasks")