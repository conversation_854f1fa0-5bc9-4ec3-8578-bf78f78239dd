"""
MCP Integration Module for GraphRAG + DeepSearch

This module provides Model Context Protocol (MCP) integration for the GraphRAG + DeepSearch project,
enabling real-time communication, streaming responses, and tool coordination.

Key Components:
- MCP Server: FastMCP-based server with tool registration
- MCP Client: Client for connecting to MCP servers
- Streaming Tools: Real-time streaming capabilities
- Agent Coordination: Multi-agent coordination through MCP
- Cache Integration: Distributed caching with MCP
"""

from .server import GraphRAGMCPServer
from .client import GraphRAGMCPClient
from .tools import (
    AgentCoordinationTools,
    SearchTools,
    CacheTools,
    KnowledgeGraphTools
)
from .streaming import StreamingManager
from .config import MCPConfig

__version__ = "0.1.0"
__author__ = "GraphRAG + DeepSearch Team"

__all__ = [
    "GraphRAGMCPServer",
    "GraphRAGMCPClient", 
    "AgentCoordinationTools",
    "SearchTools",
    "CacheTools",
    "KnowledgeGraphTools",
    "StreamingManager",
    "MCPConfig"
]