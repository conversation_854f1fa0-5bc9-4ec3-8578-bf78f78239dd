#!/usr/bin/env python3
"""
GraphRAG MCP Integration Example

This example shows how to integrate the MCP server with your existing
GraphRAG application. Use this as a template for your own integration.
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mcp_integration.server import create_graphrag_mcp_server
from mcp_integration.client import mcp_client_context
from mcp_integration.config import MCPConfig
from fastapi import FastAPI
import uvicorn


class GraphRAGMCPIntegration:
    """Example integration class showing how to use MCP with GraphRAG"""
    
    def __init__(self):
        self.mcp_server = None
        self.app = None
        self.logger = logging.getLogger("graphrag_mcp_integration")
    
    async def setup_server(self):
        """Setup the MCP server integrated with FastAPI"""
        
        # Create FastAPI app
        self.app = FastAPI(
            title="GraphRAG + MCP Integration Example",
            description="Example showing GraphRAG with MCP integration",
            version="0.1.0"
        )
        
        # Add your existing GraphRAG endpoints
        @self.app.get("/")
        async def root():
            return {
                "message": "GraphRAG + MCP Integration Example",
                "mcp_endpoint": "/mcp",
                "graphrag_endpoints": ["/search", "/agents", "/graph"]
            }
        
        # Example GraphRAG endpoints (replace with your actual implementation)
        @self.app.post("/search")
        async def search_endpoint(query: str, search_type: str = "hybrid"):
            """Example search endpoint that could use MCP internally"""
            
            # You could use MCP client here to call MCP tools
            async with mcp_client_context("http://localhost:8001") as client:
                if search_type == "local":
                    result = await client.search_local(query)
                elif search_type == "global":
                    result = await client.search_global(query)
                else:
                    result = await client.search_hybrid(query)
                
                return {"query": query, "type": search_type, "result": result}
        
        @self.app.post("/agents/coordinate")
        async def coordinate_agents_endpoint(query: str, agents: list):
            """Example agent coordination endpoint"""
            
            results = []
            async with mcp_client_context("http://localhost:8001") as client:
                async for update in client.coordinate_agents(query, agents):
                    results.append(update)
                    if len(results) >= 10:  # Limit for example
                        break
            
            return {"query": query, "agents": agents, "updates": results}
        
        # Create MCP configuration
        config = MCPConfig(
            server_name="GraphRAG-Example-MCP",
            host="localhost",
            port=8001,
            enable_streaming=True,
            log_level="INFO"
        )
        
        # Create and integrate MCP server
        self.mcp_server = create_graphrag_mcp_server(config, self.app)
        
        # Start background tasks
        await self.mcp_server.start_background_tasks()
        
        self.logger.info("✅ GraphRAG MCP Integration setup complete")
    
    async def run_server(self):
        """Run the integrated server"""
        await self.setup_server()
        
        config = uvicorn.Config(
            self.app,
            host="localhost",
            port=8001,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        
        self.logger.info("🚀 Starting GraphRAG + MCP Server on http://localhost:8001")
        self.logger.info("📖 API Documentation: http://localhost:8001/docs")
        self.logger.info("🔧 MCP Tools: http://localhost:8001/mcp")
        
        await server.serve()
    
    async def example_client_usage(self):
        """Example of how to use the MCP client"""
        
        print("🧪 Example MCP Client Usage")
        print("-" * 40)
        
        async with mcp_client_context("http://localhost:8001") as client:
            
            # Example 1: Simple search
            print("1. Simple Search:")
            result = await client.search_local("artificial intelligence")
            print(f"   Found {len(result.get('results', []))} results")
            
            # Example 2: Streaming search
            print("\n2. Streaming Search:")
            count = 0
            async for item in client.stream_search_results("machine learning"):
                if item.get("error"):
                    break
                print(f"   Stream item {count + 1}: {item.get('content', '')[:30]}...")
                count += 1
                if count >= 3:
                    break
            
            # Example 3: Agent coordination
            print("\n3. Agent Coordination:")
            agents = ["naive_rag_agent", "graph_agent"]
            async for update in client.coordinate_agents("How does AI work?", agents):
                if update.get("error"):
                    break
                print(f"   Agent {update.get('agent_id', 'unknown')}: {update.get('status', 'unknown')}")
                if update.get('status') == 'completed':
                    break
            
            # Example 4: Cache usage
            print("\n4. Cache Usage:")
            await client.set_cache_entry("example_key", {"data": "example_value"})
            cached = await client.get_cache_entry("example_key")
            print(f"   Cached data: {cached.get('value', 'Not found')}")
            
            # Example 5: Knowledge graph exploration
            print("\n5. Knowledge Graph:")
            count = 0
            async for node in client.explore_knowledge_graph("artificial_intelligence", max_depth=2):
                if node.get("error"):
                    break
                if node.get("element_type") == "node":
                    print(f"   Node: {node.get('node_id', 'unknown')}")
                    count += 1
                if count >= 3:
                    break


async def main():
    """Main function - choose what to run"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    if len(sys.argv) > 1 and sys.argv[1] == "client":
        # Run client example
        integration = GraphRAGMCPIntegration()
        await integration.example_client_usage()
    else:
        # Run server
        integration = GraphRAGMCPIntegration()
        await integration.run_server()


if __name__ == "__main__":
    print("GraphRAG MCP Integration Example")
    print("Usage:")
    print("  python mcp_example.py        # Run server")
    print("  python mcp_example.py client # Run client example")
    print()
    
    asyncio.run(main())
