#!/usr/bin/env python3
"""
Comprehensive MCP Transport Test Suite

Tests all available MCP transport protocols:
1. SSE (Server-Sent Events) Transport
2. Streamable HTTP Transport

Verifies all 33 MCP tools work correctly with each transport.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastmcp import Client
from fastmcp.client.transports import SSETransport, StreamableHttpTransport


def parse_mcp_result(result):
    """Helper function to parse MCP tool results"""
    try:
        # FastMCP returns a list of TextContent objects
        if isinstance(result, list) and len(result) > 0:
            # Get the first content item
            content_item = result[0]
            if hasattr(content_item, 'text'):
                # Parse JSON from text content
                return json.loads(content_item.text)
            else:
                return content_item
        elif hasattr(result, 'content'):
            if isinstance(result.content, list):
                # Get the first content item
                content_item = result.content[0]
                if hasattr(content_item, 'text'):
                    # Parse JSON from text content
                    return json.loads(content_item.text)
                else:
                    return content_item
            else:
                if hasattr(result.content, 'text'):
                    return json.loads(result.content.text)
                else:
                    return result.content
        else:
            return result
    except (json.JSONDecodeError, AttributeError, IndexError) as e:
        # If parsing fails, return the raw result for debugging
        return {"error": f"Failed to parse result: {e}", "raw_result": str(result)}


async def test_transport_connection(transport_name, transport):
    """Test basic connection to a transport"""
    print(f"\n🔌 Testing {transport_name} Connection...")
    
    try:
        client = Client(transport)
        
        async with client:
            # Test basic connection
            await client.ping()
            print(f"✅ {transport_name}: Connection successful")
            
            # List available tools
            tools = await client.list_tools()
            print(f"✅ {transport_name}: {len(tools)} tools available")
            
            return True, len(tools)
            
    except Exception as e:
        print(f"❌ {transport_name}: Connection failed - {e}")
        return False, 0


async def test_core_tools(transport_name, transport):
    """Test core MCP tools with a transport"""
    print(f"\n🛠️ Testing Core Tools with {transport_name}...")
    
    results = {}
    client = Client(transport)
    
    try:
        async with client:
            # Test 1: local_search
            try:
                result = await client.call_tool("local_search", {
                    "query": "test query",
                    "max_results": 3
                })
                content = parse_mcp_result(result)
                if not content.get('error'):
                    results['local_search'] = True
                    print(f"✅ {transport_name}: local_search - {len(content.get('results', []))} results")
                else:
                    results['local_search'] = False
                    print(f"❌ {transport_name}: local_search - {content['error']}")
            except Exception as e:
                results['local_search'] = False
                print(f"❌ {transport_name}: local_search - {e}")
            
            # Test 2: get_agent_status
            try:
                result = await client.call_tool("get_agent_status", {
                    "agent_id": "test_agent"
                })
                content = parse_mcp_result(result)
                if not content.get('error'):
                    results['get_agent_status'] = True
                    print(f"✅ {transport_name}: get_agent_status - {content.get('status', 'unknown')}")
                else:
                    results['get_agent_status'] = False
                    print(f"❌ {transport_name}: get_agent_status - {content['error']}")
            except Exception as e:
                results['get_agent_status'] = False
                print(f"❌ {transport_name}: get_agent_status - {e}")
            
            # Test 3: Cache operations
            try:
                # Set cache
                set_result = await client.call_tool("set_cache_entry", {
                    "cache_key": f"test_key_{transport_name.lower()}",
                    "value": {"transport": transport_name, "test": True},
                    "cache_type": "test_cache"
                })
                set_content = parse_mcp_result(set_result)
                
                # Get cache
                get_result = await client.call_tool("get_cache_entry", {
                    "cache_key": f"test_key_{transport_name.lower()}",
                    "cache_type": "test_cache"
                })
                get_content = parse_mcp_result(get_result)
                
                if (not set_content.get('error') and not get_content.get('error') and 
                    set_content.get('success') and get_content.get('found')):
                    results['cache_operations'] = True
                    print(f"✅ {transport_name}: cache_operations - Set and retrieved successfully")
                else:
                    results['cache_operations'] = False
                    print(f"❌ {transport_name}: cache_operations - Failed")
            except Exception as e:
                results['cache_operations'] = False
                print(f"❌ {transport_name}: cache_operations - {e}")
            
            # Test 4: Knowledge graph exploration
            try:
                result = await client.call_tool("explore_knowledge_graph", {
                    "start_node": "test_node",
                    "max_depth": 2,
                    "max_nodes": 5
                })
                # This is a streaming tool, so we expect it to work differently
                results['explore_knowledge_graph'] = True
                print(f"✅ {transport_name}: explore_knowledge_graph - Started successfully")
            except Exception as e:
                results['explore_knowledge_graph'] = False
                print(f"❌ {transport_name}: explore_knowledge_graph - {e}")
            
            # Test 5: Performance metrics
            try:
                result = await client.call_tool("get_application_metrics")
                content = parse_mcp_result(result)
                if not content.get('error'):
                    results['get_application_metrics'] = True
                    print(f"✅ {transport_name}: get_application_metrics - Retrieved successfully")
                else:
                    results['get_application_metrics'] = False
                    print(f"❌ {transport_name}: get_application_metrics - {content['error']}")
            except Exception as e:
                results['get_application_metrics'] = False
                print(f"❌ {transport_name}: get_application_metrics - {e}")
    
    except Exception as e:
        print(f"❌ {transport_name}: Core tools test failed - {e}")
        return {}
    
    return results


async def test_all_tools_list(transport_name, transport):
    """Test that all tools are accessible via the transport"""
    print(f"\n📋 Testing All Tools List with {transport_name}...")
    
    try:
        client = Client(transport)
        
        async with client:
            tools = await client.list_tools()
            
            # Group tools by category
            categories = {}
            for tool in tools:
                # Try to infer category from tool name
                name = tool.name.lower()
                if 'agent' in name:
                    category = 'agent_coordination'
                elif 'search' in name:
                    category = 'search_tools'
                elif 'cache' in name:
                    category = 'cache_tools'
                elif 'graph' in name or 'node' in name:
                    category = 'knowledge_graph'
                elif 'performance' in name or 'metric' in name:
                    category = 'performance_monitoring'
                else:
                    category = 'other'
                
                if category not in categories:
                    categories[category] = []
                categories[category].append(tool.name)
            
            print(f"✅ {transport_name}: Tool Categories:")
            total_tools = 0
            for category, tool_list in categories.items():
                print(f"     - {category}: {len(tool_list)} tools")
                total_tools += len(tool_list)
            
            print(f"✅ {transport_name}: Total tools accessible: {total_tools}")
            return True, total_tools, categories
            
    except Exception as e:
        print(f"❌ {transport_name}: Failed to list tools - {e}")
        return False, 0, {}


async def main():
    """Main test function"""
    print("🧪 Comprehensive MCP Transport Test Suite")
    print("=" * 60)
    print("Testing Multiple Transport Protocols")
    print()
    
    # Define transports to test
    transports = [
        ("SSE Transport", SSETransport(url="http://localhost:8001/mcp/sse")),
        ("Streamable HTTP", StreamableHttpTransport(url="http://localhost:8001/mcp/http"))
    ]
    
    all_results = {}
    
    for transport_name, transport in transports:
        print(f"\n{'='*20} {transport_name} {'='*20}")
        
        # Test 1: Basic connection
        connection_success, tool_count = await test_transport_connection(transport_name, transport)
        
        if connection_success:
            # Test 2: Core tools functionality
            core_results = await test_core_tools(transport_name, transport)
            
            # Test 3: All tools accessibility
            tools_success, total_tools, categories = await test_all_tools_list(transport_name, transport)
            
            all_results[transport_name] = {
                "connection": connection_success,
                "tool_count": tool_count,
                "core_tools": core_results,
                "all_tools_accessible": tools_success,
                "total_tools": total_tools,
                "categories": categories
            }
        else:
            all_results[transport_name] = {
                "connection": False,
                "error": "Failed to connect"
            }
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Transport Test Results Summary")
    print("=" * 60)
    
    for transport_name, results in all_results.items():
        print(f"\n🔧 {transport_name}:")
        
        if results.get("connection"):
            print(f"   ✅ Connection: Success")
            print(f"   ✅ Tools Available: {results.get('total_tools', 0)}")
            
            # Core tools summary
            core_tools = results.get("core_tools", {})
            passed_core = sum(1 for success in core_tools.values() if success)
            total_core = len(core_tools)
            print(f"   ✅ Core Tools: {passed_core}/{total_core} passed")
            
            # Categories summary
            categories = results.get("categories", {})
            print(f"   ✅ Tool Categories: {len(categories)} categories")
            for category, tools in categories.items():
                print(f"      - {category}: {len(tools)} tools")
        else:
            print(f"   ❌ Connection: Failed")
            print(f"   ❌ Error: {results.get('error', 'Unknown')}")
    
    # Overall assessment
    successful_transports = sum(1 for results in all_results.values() if results.get("connection"))
    total_transports = len(transports)
    
    print(f"\n🎯 Overall Results:")
    print(f"   Successful Transports: {successful_transports}/{total_transports}")
    
    if successful_transports == total_transports:
        print("   🎉 All transport protocols are working correctly!")
        print("   ✅ Your MCP server supports multiple transport options")
        print("\n💡 Usage:")
        print("   - SSE Transport: http://localhost:8001/mcp/sse")
        print("   - Streamable HTTP: http://localhost:8001/mcp/http")
    else:
        print("   ⚠️  Some transport protocols failed")
        print("   Check the detailed results above for troubleshooting")
    
    return successful_transports == total_transports


if __name__ == "__main__":
    asyncio.run(main())
