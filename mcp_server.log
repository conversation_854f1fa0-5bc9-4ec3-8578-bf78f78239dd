2025-06-29 23:15:42,863 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-29 23:15:42,864 - mcp_server_startup - ERROR - ❌ Server error: FastMCP.__init__() got an unexpected keyword argument 'description'
2025-06-30 00:20:21,910 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:20:21,916 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:20:21,916 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:20:21,916 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:20:21,916 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:20:21,916 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:20:21,916 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:20:21,917 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:20:21,917 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:20:21,917 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:20:21,917 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:20:21,917 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:20:21,917 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:20:21,917 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:20:21,917 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:20:21,920 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:20:21,920 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:20:21,922 - mcp_server_startup - ERROR - ❌ Server error: Functions with **kwargs are not supported as tools
2025-06-30 00:29:59,381 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:29:59,388 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:29:59,388 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:29:59,388 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:29:59,388 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:29:59,388 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:29:59,388 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:29:59,388 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:29:59,388 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:29:59,388 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:29:59,388 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:29:59,388 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:29:59,388 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:29:59,388 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:29:59,388 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:29:59,392 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:29:59,392 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:29:59,395 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:29:59,395 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:29:59,398 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:29:59,398 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:29:59,401 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:29:59,402 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:29:59,404 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:29:59,405 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:29:59,405 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:29:59,405 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:29:59,405 - mcp_server_startup - ERROR - ❌ Server error: create_sse_app() missing 2 required positional arguments: 'message_path' and 'sse_path'
2025-06-30 00:30:46,983 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:30:46,989 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:30:46,989 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:30:46,989 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:30:46,990 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:30:46,990 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:30:46,990 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:30:46,990 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:30:46,990 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:30:46,990 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:30:46,990 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:30:46,990 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:30:46,990 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:30:46,990 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:30:46,990 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:30:46,993 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:30:46,993 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:30:46,996 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:30:46,996 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:30:46,999 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:30:46,999 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:30:47,002 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:30:47,002 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:30:47,005 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:30:47,006 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:30:47,006 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:30:47,006 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:30:47,008 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 00:30:47,009 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 00:30:47,009 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 00:30:47,009 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 00:30:47,009 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 00:30:47,009 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 00:30:47,051 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 00:30:47,051 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 00:30:47,051 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 00:35:07,992 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:35:07,998 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:35:07,998 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:35:07,998 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:35:07,998 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:35:07,998 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:35:07,999 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:35:07,999 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:35:07,999 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:35:07,999 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:35:07,999 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:35:07,999 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:35:07,999 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:35:07,999 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:35:07,999 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:35:08,002 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:35:08,002 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:35:08,005 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:35:08,006 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:35:08,008 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:35:08,008 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:35:08,012 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:35:08,012 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:35:08,013 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:35:08,014 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:35:08,014 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:35:08,014 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:35:08,015 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 00:35:08,015 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 00:35:08,016 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 00:35:08,016 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 00:35:08,016 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 00:35:08,016 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 00:35:08,026 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 00:35:08,026 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 00:35:08,026 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 00:35:42,323 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:35:42,329 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:35:42,329 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:35:42,329 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:35:42,329 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:35:42,329 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:35:42,329 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:35:42,329 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:35:42,329 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:35:42,329 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:35:42,329 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:35:42,329 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:35:42,329 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:35:42,329 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:35:42,329 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:35:42,332 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:35:42,332 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:35:42,335 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:35:42,335 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:35:42,338 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:35:42,338 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:35:42,341 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:35:42,341 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:35:42,342 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:35:42,343 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:35:42,343 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:35:42,343 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:35:42,344 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 00:35:42,344 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 00:35:42,344 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 00:35:42,345 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 00:35:42,345 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 00:35:42,345 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 00:35:42,354 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 00:35:42,354 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 00:35:42,355 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 00:36:48,505 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:36:48,511 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:36:48,511 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:36:48,511 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:36:48,511 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:36:48,511 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:36:48,511 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:36:48,511 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:36:48,511 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:36:48,511 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:36:48,511 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:36:48,511 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:36:48,511 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:36:48,512 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:36:48,512 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:36:48,515 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:36:48,515 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:36:48,518 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:36:48,518 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:36:48,520 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:36:48,520 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:36:48,523 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:36:48,523 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:36:48,525 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:36:48,526 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:36:48,526 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:36:48,526 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:36:48,527 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 00:36:48,527 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 00:36:48,527 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 00:36:48,527 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 00:36:48,527 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 00:36:48,527 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 00:36:48,537 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 00:36:48,537 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 00:36:48,537 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 00:42:13,003 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-06-30 00:42:13,007 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:42:13,129 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:42:13,313 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:42:13,416 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:42:13,522 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:42:13,522 - FastMCP.fastmcp.tools.tool_manager - ERROR - Error calling tool 'get_server_info'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool_manager.py", line 185, in call_tool
    return await tool.run(arguments)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool.py", line 204, in run
    result = await result
             ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/mcp_integration/server.py", line 136, in get_server_info
    "tool_count": len(self.mcp_app.tools)
                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'FastMCP' object has no attribute 'tools'. Did you mean: 'tool'?
2025-06-30 00:44:01,340 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-06-30 00:44:01,344 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:44:01,469 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:44:01,579 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:44:01,687 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:44:01,882 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:44:01,892 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:44:01,893 - FastMCP.fastmcp.tools.tool_manager - ERROR - Error calling tool 'get_server_info'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool_manager.py", line 185, in call_tool
    return await tool.run(arguments)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool.py", line 204, in run
    result = await result
             ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/mcp_integration/server.py", line 136, in get_server_info
    "tool_count": len(self.mcp_app.tools)
                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'FastMCP' object has no attribute 'tools'. Did you mean: 'tool'?
2025-06-30 00:45:31,428 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:45:31,435 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:45:31,435 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:45:31,435 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:45:31,435 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:45:31,435 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:45:31,435 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:45:31,435 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:45:31,435 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:45:31,435 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:45:31,435 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:45:31,435 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:45:31,435 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:45:31,435 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:45:31,435 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:45:31,438 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:45:31,438 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:45:31,441 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:45:31,442 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:45:31,444 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:45:31,445 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:45:31,447 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:45:31,448 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:45:31,449 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:45:31,450 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:45:31,450 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:45:31,450 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:45:31,452 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 00:45:31,452 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 00:45:31,453 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 00:45:31,453 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 00:45:31,453 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 00:45:31,453 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 00:45:31,467 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 00:45:31,467 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 00:45:31,467 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 00:45:47,914 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-06-30 00:45:47,921 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:45:48,047 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:45:48,222 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:45:48,331 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:45:48,452 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:45:48,465 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:45:48,465 - FastMCP.fastmcp.tools.tool_manager - ERROR - Error calling tool 'get_server_info'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool_manager.py", line 185, in call_tool
    return await tool.run(arguments)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool.py", line 204, in run
    result = await result
             ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/mcp_integration/server.py", line 136, in get_server_info
    "tool_count": len(self.mcp_app.tools)
                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'FastMCP' object has no attribute 'tools'. Did you mean: 'tool'?
2025-06-30 00:46:24,470 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:47:29,258 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-06-30 00:47:29,263 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:47:29,378 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:47:29,480 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:47:29,589 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:47:29,593 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:47:29,700 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-06-30 00:47:29,715 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-06-30 00:47:29,729 - FastMCP.fastmcp.tools.tool_manager - ERROR - Error calling tool 'get_server_info'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool_manager.py", line 185, in call_tool
    return await tool.run(arguments)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/tools/tool.py", line 204, in run
    result = await result
             ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/mcp_integration/server.py", line 136, in get_server_info
    "tool_count": len(self.mcp_app.tools)
                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'FastMCP' object has no attribute 'tools'. Did you mean: 'tool'?
2025-06-30 00:53:24,291 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 00:53:24,297 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 00:53:24,297 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 00:53:24,297 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 00:53:24,297 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 00:53:24,297 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 00:53:24,297 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 00:53:24,297 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 00:53:24,297 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 00:53:24,297 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 00:53:24,297 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 00:53:24,297 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 00:53:24,297 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 00:53:24,297 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 00:53:24,297 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 00:53:24,300 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 00:53:24,300 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 00:53:24,303 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 00:53:24,303 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 00:53:24,306 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 00:53:24,306 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 00:53:24,308 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 00:53:24,308 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 00:53:24,310 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 00:53:24,311 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 00:53:24,311 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 00:53:24,311 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 00:53:24,313 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 00:53:24,313 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 00:53:24,313 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 00:53:24,313 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 00:53:24,313 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 00:53:24,314 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 00:53:24,324 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 00:53:24,325 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 00:53:24,325 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:39:02,264 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 01:39:02,272 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 01:39:02,272 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 01:39:02,272 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 01:39:02,272 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 01:39:02,272 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 01:39:02,272 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 01:39:02,272 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 01:39:02,272 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 01:39:02,272 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 01:39:02,272 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 01:39:02,272 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 01:39:02,272 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 01:39:02,272 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 01:39:02,272 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 01:39:02,275 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 01:39:02,275 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 01:39:02,281 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 01:39:02,281 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 01:39:02,284 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 01:39:02,284 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 01:39:02,288 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 01:39:02,288 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 01:39:02,291 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 01:39:02,293 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 01:39:02,293 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 01:39:02,293 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 01:39:02,298 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 01:39:02,298 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp
2025-06-30 01:39:02,299 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 01:39:02,300 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 01:39:02,300 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 01:39:02,301 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 01:39:02,301 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 01:39:02,301 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 01:39:02,315 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 01:39:02,316 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 01:39:02,316 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:39:32,429 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 01:39:32,436 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 01:39:32,436 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 01:39:32,436 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 01:39:32,436 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 01:39:32,436 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 01:39:32,436 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 01:39:32,436 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 01:39:32,436 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 01:39:32,436 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 01:39:32,436 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 01:39:32,436 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 01:39:32,436 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 01:39:32,436 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 01:39:32,436 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 01:39:32,439 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 01:39:32,439 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 01:39:32,443 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 01:39:32,443 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 01:39:32,446 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 01:39:32,446 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 01:39:32,448 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 01:39:32,448 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 01:39:32,450 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 01:39:32,451 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 01:39:32,451 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 01:39:32,451 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 01:39:32,454 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 01:39:32,454 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp
2025-06-30 01:39:32,455 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 01:39:32,455 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 01:39:32,456 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 01:39:32,456 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 01:39:32,456 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 01:39:32,456 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 01:39:32,467 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 01:39:32,467 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 01:39:32,467 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:40:11,281 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 01:40:11,288 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 01:40:11,288 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 01:40:11,289 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 01:40:11,289 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 01:40:11,289 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 01:40:11,289 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 01:40:11,289 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 01:40:11,289 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 01:40:11,289 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 01:40:11,289 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 01:40:11,289 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 01:40:11,289 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 01:40:11,289 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 01:40:11,289 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 01:40:11,292 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 01:40:11,292 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 01:40:11,295 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 01:40:11,295 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 01:40:11,298 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 01:40:11,298 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 01:40:11,300 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 01:40:11,300 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 01:40:11,302 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 01:40:11,303 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 01:40:11,303 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 01:40:11,303 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 01:40:11,307 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 01:40:11,307 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp
2025-06-30 01:40:11,308 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 01:40:11,308 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 01:40:11,308 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 01:40:11,309 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 01:40:11,309 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 01:40:11,309 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 01:40:11,320 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 01:40:11,320 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 01:40:11,320 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:49:52,671 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 01:49:52,678 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 01:49:52,678 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 01:49:52,678 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 01:49:52,678 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 01:49:52,678 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 01:49:52,678 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 01:49:52,678 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 01:49:52,678 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 01:49:52,678 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 01:49:52,678 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 01:49:52,678 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 01:49:52,678 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 01:49:52,678 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 01:49:52,678 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 01:49:52,682 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 01:49:52,682 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 01:49:52,687 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 01:49:52,687 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 01:49:52,690 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 01:49:52,690 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 01:49:52,694 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 01:49:52,694 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 01:49:52,696 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 01:49:52,697 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 01:49:52,697 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 01:49:52,697 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 01:49:52,700 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 01:49:52,700 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 01:49:52,702 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 01:49:52,702 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 01:49:52,702 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 01:49:52,702 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 01:49:52,702 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 01:49:52,703 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 01:49:52,714 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 01:49:52,715 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 01:49:52,715 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:52:10,131 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 01:52:10,138 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 01:52:10,138 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 01:52:10,138 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 01:52:10,138 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 01:52:10,138 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 01:52:10,138 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 01:52:10,138 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 01:52:10,138 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 01:52:10,138 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 01:52:10,138 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 01:52:10,138 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 01:52:10,138 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 01:52:10,138 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 01:52:10,139 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 01:52:10,142 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 01:52:10,142 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 01:52:10,145 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 01:52:10,145 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 01:52:10,149 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 01:52:10,149 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 01:52:10,153 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 01:52:10,153 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 01:52:10,155 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 01:52:10,156 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 01:52:10,156 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 01:52:10,156 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 01:52:10,159 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 01:52:10,159 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 01:52:10,160 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 01:52:10,160 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 01:52:10,161 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 01:52:10,161 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 01:52:10,161 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 01:52:10,161 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 01:52:10,172 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 01:52:10,173 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 01:52:10,173 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:53:29,135 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 01:53:29,141 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 01:53:29,141 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 01:53:29,141 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 01:53:29,141 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 01:53:29,141 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 01:53:29,142 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 01:53:29,142 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 01:53:29,142 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 01:53:29,142 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 01:53:29,142 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 01:53:29,142 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 01:53:29,142 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 01:53:29,142 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 01:53:29,142 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 01:53:29,145 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 01:53:29,145 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 01:53:29,148 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 01:53:29,148 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 01:53:29,151 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 01:53:29,151 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 01:53:29,154 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 01:53:29,154 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 01:53:29,156 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 01:53:29,157 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 01:53:29,157 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 01:53:29,157 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 01:53:29,161 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 01:53:29,161 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 01:53:29,162 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 01:53:29,162 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 01:53:29,163 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 01:53:29,163 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 01:53:29,163 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 01:53:29,163 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 01:53:29,174 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 01:53:29,174 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 01:53:29,174 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 01:54:23,408 - FastMCP.fastmcp.server.http - ERROR - Original RuntimeError from mcp library: Task group is not initialized. Make sure to use run().
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/server/http.py", line 289, in handle_streamable_http
    await session_manager.handle_request(scope, receive, send)
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/mcp/server/streamable_http_manager.py", line 137, in handle_request
    raise RuntimeError("Task group is not initialized. Make sure to use run().")
RuntimeError: Task group is not initialized. Make sure to use run().
2025-06-30 02:01:32,951 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 02:01:32,958 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 02:01:32,958 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 02:01:32,958 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 02:01:32,958 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 02:01:32,958 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 02:01:32,958 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 02:01:32,958 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 02:01:32,958 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 02:01:32,958 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 02:01:32,958 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 02:01:32,958 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 02:01:32,958 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 02:01:32,958 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 02:01:32,958 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 02:01:32,962 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 02:01:32,962 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 02:01:32,965 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 02:01:32,965 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 02:01:32,968 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 02:01:32,968 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 02:01:32,972 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 02:01:32,972 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 02:01:32,974 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 02:01:32,975 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 02:01:32,975 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 02:01:32,983 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 02:01:32,983 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 02:01:32,983 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 02:01:32,983 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 02:01:32,983 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 02:01:32,983 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 02:01:32,983 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 02:01:32,983 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 02:01:32,983 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 02:01:32,983 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 02:01:32,983 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 02:01:32,983 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 02:01:32,983 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 02:01:32,983 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 02:01:32,985 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 02:01:32,985 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 02:01:32,988 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 02:01:32,989 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 02:01:32,991 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 02:01:32,991 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 02:01:32,995 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 02:01:32,995 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 02:01:32,997 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 02:01:32,998 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 02:01:32,998 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 02:01:32,998 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 02:01:32,999 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 02:01:32,999 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 02:01:33,000 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 02:01:33,000 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 02:01:33,001 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 02:01:33,001 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 02:01:33,001 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 02:01:33,001 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 02:01:33,015 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 02:01:33,016 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 02:01:33,016 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 02:01:33,016 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-06-30 02:02:04,375 - FastMCP.fastmcp.server.http - ERROR - Original RuntimeError from mcp library: Task group is not initialized. Make sure to use run().
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/server/http.py", line 289, in handle_streamable_http
    await session_manager.handle_request(scope, receive, send)
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/mcp/server/streamable_http_manager.py", line 137, in handle_request
    raise RuntimeError("Task group is not initialized. Make sure to use run().")
RuntimeError: Task group is not initialized. Make sure to use run().
2025-06-30 02:06:48,632 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 02:06:48,639 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 02:06:48,639 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 02:06:48,639 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 02:06:48,639 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 02:06:48,639 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 02:06:48,639 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 02:06:48,639 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 02:06:48,639 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 02:06:48,639 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 02:06:48,639 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 02:06:48,639 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 02:06:48,639 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 02:06:48,639 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 02:06:48,639 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 02:06:48,642 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 02:06:48,642 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 02:06:48,646 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 02:06:48,646 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 02:06:48,650 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 02:06:48,650 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 02:06:48,654 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 02:06:48,654 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 02:06:48,656 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 02:06:48,656 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 02:06:48,656 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 02:06:48,660 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 02:06:48,661 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 02:06:48,661 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 02:06:48,662 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 02:06:48,662 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 02:06:48,662 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 02:06:48,662 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 02:06:48,663 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 02:06:48,663 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 02:06:48,674 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 02:06:48,674 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 02:06:48,674 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 02:06:48,674 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-06-30 02:07:48,165 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 02:07:48,173 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 02:07:48,173 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 02:07:48,173 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 02:07:48,173 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 02:07:48,173 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 02:07:48,173 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 02:07:48,173 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 02:07:48,173 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 02:07:48,173 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 02:07:48,173 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 02:07:48,173 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 02:07:48,173 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 02:07:48,173 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 02:07:48,173 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 02:07:48,177 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 02:07:48,177 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 02:07:48,180 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 02:07:48,180 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 02:07:48,183 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 02:07:48,183 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 02:07:48,186 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 02:07:48,186 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 02:07:48,188 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 02:07:48,188 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 02:07:48,188 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 02:07:48,192 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 02:07:48,192 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 02:07:48,193 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 02:07:48,193 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 02:07:48,193 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 02:07:48,194 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 02:07:48,194 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 02:07:48,194 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 02:07:48,194 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 02:07:48,214 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 02:07:48,215 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 02:07:48,215 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 02:07:48,215 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-06-30 02:08:26,925 - FastMCP.fastmcp.server.http - ERROR - Original RuntimeError from mcp library: Task group is not initialized. Make sure to use run().
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/fastmcp/server/http.py", line 289, in handle_streamable_http
    await session_manager.handle_request(scope, receive, send)
  File "/Users/<USER>/Documents/Code/Robot/graph-rag-agent/.venv/lib/python3.12/site-packages/mcp/server/streamable_http_manager.py", line 137, in handle_request
    raise RuntimeError("Task group is not initialized. Make sure to use run().")
RuntimeError: Task group is not initialized. Make sure to use run().
2025-06-30 02:23:02,009 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 02:23:02,016 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 02:23:02,016 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 02:23:02,016 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 02:23:02,016 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 02:23:02,016 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 02:23:02,016 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 02:23:02,016 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 02:23:02,016 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 02:23:02,016 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 02:23:02,016 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 02:23:02,016 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 02:23:02,016 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 02:23:02,016 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 02:23:02,016 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 02:23:02,019 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 02:23:02,019 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 02:23:02,022 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 02:23:02,022 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 02:23:02,025 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 02:23:02,025 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 02:23:02,028 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 02:23:02,028 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 02:23:02,029 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 02:23:02,030 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 02:23:02,030 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 02:23:02,034 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 02:23:02,034 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Using cached Streamable HTTP app for consistent lifespan
2025-06-30 02:23:02,034 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 02:23:02,034 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 02:23:02,036 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 02:23:02,036 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 02:23:02,036 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 02:23:02,036 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 02:23:02,036 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 02:23:02,036 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 02:23:02,047 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 02:23:02,047 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 02:23:02,047 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 02:23:02,047 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-06-30 02:23:02,152 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-06-30 02:23:16,114 - mcp_server_startup - INFO - 🛑 Server stopped by user
2025-06-30 02:23:16,115 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-06-30 02:23:27,189 - mcp_server_startup - INFO - 🚀 Starting GraphRAG MCP Server...
2025-06-30 02:23:27,195 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager initialized
2025-06-30 02:23:27,195 - mcp_middleware.GraphRAG-DeepSearch-MCP - INFO - MCP Middleware initialized
2025-06-30 02:23:27,195 - mcp_tools.AgentCoordinationTools - INFO - Initialized AgentCoordinationTools (enabled: True)
2025-06-30 02:23:27,195 - mcp_tools.AgentCoordinationTools - INFO - Agent coordination configured: strategy=parallel, max_agents=5
2025-06-30 02:23:27,196 - mcp_tools.SearchTools - INFO - Initialized SearchTools (enabled: True)
2025-06-30 02:23:27,196 - mcp_tools.SearchTools - INFO - Search tools configured: streaming=True
2025-06-30 02:23:27,196 - mcp_tools.CacheTools - INFO - Initialized CacheTools (enabled: True)
2025-06-30 02:23:27,196 - mcp_tools.CacheTools - INFO - Cache tools configured: distributed=True
2025-06-30 02:23:27,196 - mcp_tools.KnowledgeGraphTools - INFO - Initialized KnowledgeGraphTools (enabled: True)
2025-06-30 02:23:27,196 - mcp_tools.KnowledgeGraphTools - INFO - Knowledge graph tools configured: max_depth=5
2025-06-30 02:23:27,196 - mcp_tools.PerformanceMonitoringTools - INFO - Initialized PerformanceMonitoringTools (enabled: True)
2025-06-30 02:23:27,196 - mcp_tools.PerformanceMonitoringTools - INFO - Performance monitoring configured: enabled=True
2025-06-30 02:23:27,196 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Initialized tool managers: ['agent_coordination', 'search_tools', 'cache_tools', 'knowledge_graph_tools', 'performance_monitoring']
2025-06-30 02:23:27,196 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: agent_coordination
2025-06-30 02:23:27,199 - mcp_tools.AgentCoordinationTools - INFO - Registered 4 agent coordination tools
2025-06-30 02:23:27,199 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: search_tools
2025-06-30 02:23:27,202 - mcp_tools.SearchTools - INFO - Registered 6 search tools
2025-06-30 02:23:27,202 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: cache_tools
2025-06-30 02:23:27,205 - mcp_tools.CacheTools - INFO - Registered 7 cache tools
2025-06-30 02:23:27,205 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: knowledge_graph_tools
2025-06-30 02:23:27,209 - mcp_tools.KnowledgeGraphTools - INFO - Registered 6 knowledge graph tools
2025-06-30 02:23:27,210 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Registering tools for category: performance_monitoring
2025-06-30 02:23:27,212 - mcp_tools.PerformanceMonitoringTools - INFO - Registered 6 performance monitoring tools
2025-06-30 02:23:27,213 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - All tools registered successfully
2025-06-30 02:23:27,213 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - GraphRAG MCP Server initialized: GraphRAG-DeepSearch-MCP
2025-06-30 02:23:27,217 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Integrating MCP server with FastAPI at /mcp
2025-06-30 02:23:27,217 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Using cached Streamable HTTP app for consistent lifespan
2025-06-30 02:23:27,218 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ SSE Transport: /mcp/sse
2025-06-30 02:23:27,218 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - ✅ Streamable HTTP Transport: /mcp/http
2025-06-30 02:23:27,219 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - MCP integration with FastAPI completed
2025-06-30 02:23:27,219 - mcp_server.GraphRAG-DeepSearch-MCP - INFO - Started 3 background tasks
2025-06-30 02:23:27,220 - mcp_server_startup - INFO - ✅ MCP Server starting on http://localhost:8001
2025-06-30 02:23:27,220 - mcp_server_startup - INFO - 📖 API Documentation: http://localhost:8001/docs
2025-06-30 02:23:27,221 - mcp_server_startup - INFO - 🔧 MCP Endpoint: http://localhost:8001/mcp
2025-06-30 02:23:27,221 - mcp_server_startup - INFO - ❤️  Health Check: http://localhost:8001/health
2025-06-30 02:23:27,233 - mcp_streaming.GraphRAG-DeepSearch-MCP - INFO - Streaming Manager started
2025-06-30 02:23:27,233 - mcp_tools.PerformanceMonitoringTools - INFO - Started performance monitoring task
2025-06-30 02:23:27,233 - mcp_tools.CacheTools - INFO - Started cache synchronization task
2025-06-30 02:23:27,234 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
