#!/usr/bin/env python3
"""
GraphRAG MCP Client Test Script

This script demonstrates how to connect to and use the GraphRAG MCP server.
Run this after starting the MCP server to test the integration.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mcp_integration.client import create_graphrag_mcp_client, mcp_client_context


async def test_basic_connection():
    """Test basic connection to MCP server"""
    print("🔌 Testing basic MCP connection...")
    
    try:
        async with mcp_client_context("http://localhost:8001") as client:
            # Test server status
            status = await client.get_server_status()
            print(f"✅ Server Status: {status.get('server_name', 'Unknown')}")
            
            # Get available tools
            tools = client.get_available_tools()
            print(f"🔧 Available Tools: {len(tools)} tools loaded")
            
            for tool_name, tool_info in list(tools.items())[:5]:  # Show first 5
                print(f"   - {tool_name}: {tool_info.get('description', 'No description')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False


async def test_search_functionality():
    """Test search functionality"""
    print("\n🔍 Testing search functionality...")
    
    try:
        async with mcp_client_context("http://localhost:8001") as client:
            # Test local search
            print("Testing local search...")
            local_results = await client.search_local("artificial intelligence", max_results=3)
            
            if not local_results.get("error"):
                print(f"✅ Local Search: Found {len(local_results.get('results', []))} results")
                for result in local_results.get('results', [])[:2]:
                    print(f"   - {result.get('content', 'No content')[:50]}...")
            else:
                print(f"❌ Local Search Error: {local_results.get('message')}")
            
            # Test global search
            print("Testing global search...")
            global_results = await client.search_global("machine learning")
            
            if not global_results.get("error"):
                print(f"✅ Global Search: Found {len(global_results.get('communities', []))} communities")
            else:
                print(f"❌ Global Search Error: {global_results.get('message')}")
                
    except Exception as e:
        print(f"❌ Search test failed: {e}")


async def test_streaming_functionality():
    """Test streaming functionality"""
    print("\n📡 Testing streaming functionality...")
    
    try:
        async with mcp_client_context("http://localhost:8001") as client:
            print("Testing streaming search results...")
            
            count = 0
            async for result in client.stream_search_results("AI applications", search_type="local"):
                if result.get("error"):
                    print(f"❌ Stream Error: {result.get('message')}")
                    break
                
                print(f"📦 Stream Result {count + 1}: {result.get('content', 'No content')[:40]}...")
                count += 1
                
                if count >= 3:  # Limit for demo
                    break
            
            print(f"✅ Streaming: Received {count} results")
                
    except Exception as e:
        print(f"❌ Streaming test failed: {e}")


async def test_agent_coordination():
    """Test agent coordination"""
    print("\n🤖 Testing agent coordination...")
    
    try:
        async with mcp_client_context("http://localhost:8001") as client:
            print("Testing agent coordination...")
            
            agents = ["naive_rag_agent", "graph_agent", "hybrid_agent"]
            count = 0
            
            async for update in client.coordinate_agents(
                "How does AI impact healthcare?", 
                agents=agents,
                strategy="parallel"
            ):
                if update.get("error"):
                    print(f"❌ Coordination Error: {update.get('message')}")
                    break
                
                agent_id = update.get('agent_id', 'unknown')
                status = update.get('status', 'unknown')
                print(f"🤖 Agent {agent_id}: {status}")
                count += 1
                
                if count >= 5:  # Limit for demo
                    break
            
            print(f"✅ Agent Coordination: Received {count} updates")
                
    except Exception as e:
        print(f"❌ Agent coordination test failed: {e}")


async def test_cache_functionality():
    """Test cache functionality"""
    print("\n💾 Testing cache functionality...")
    
    try:
        async with mcp_client_context("http://localhost:8001") as client:
            # Test cache set
            cache_result = await client.set_cache_entry(
                "test_key", 
                {"data": "test_value", "timestamp": "2024-01-01"},
                cache_type="test_cache"
            )
            
            if not cache_result.get("error"):
                print("✅ Cache Set: Successfully stored test data")
            else:
                print(f"❌ Cache Set Error: {cache_result.get('message')}")
            
            # Test cache get
            cached_data = await client.get_cache_entry("test_key", cache_type="test_cache")
            
            if cached_data.get("found"):
                print(f"✅ Cache Get: Retrieved data - {cached_data.get('value')}")
            else:
                print(f"❌ Cache Get: Data not found - {cached_data.get('reason')}")
                
    except Exception as e:
        print(f"❌ Cache test failed: {e}")


async def main():
    """Main test function"""
    print("🧪 GraphRAG MCP Client Test Suite")
    print("=" * 50)
    
    # Check if server is running
    print("Checking if MCP server is running on http://localhost:8001...")
    
    if not await test_basic_connection():
        print("\n❌ MCP Server is not running!")
        print("💡 Start the server first with: python run_mcp_server.py")
        return
    
    # Run all tests
    await test_search_functionality()
    await test_streaming_functionality()
    await test_agent_coordination()
    await test_cache_functionality()
    
    print("\n" + "=" * 50)
    print("🎉 MCP Client Test Suite Completed!")
    print("📖 Check the server logs for detailed information")


if __name__ == "__main__":
    asyncio.run(main())
