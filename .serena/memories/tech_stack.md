# Technology Stack

## Core Technologies
- **Python 3.10**: Primary programming language
- **FastAPI**: Backend web framework for REST APIs
- **Streamlit**: Frontend web interface
- **Neo4j**: Graph database for knowledge storage
- **LangChain/LangGraph**: LLM integration and agent orchestration
- **OpenAI API**: LLM and embedding models (via One-API proxy)

## Key Libraries
- **langchain**: LLM integration framework
- **langgraph**: Agent workflow orchestration
- **langchain_neo4j**: Neo4j integration for LangChain
- **langchain_openai**: OpenAI API integration
- **faiss-cpu**: Vector similarity search
- **sentence_transformers**: Text embeddings
- **pandas/numpy**: Data processing
- **scikit-learn**: Machine learning utilities
- **rich**: Enhanced console output
- **tqdm**: Progress bars
- **uvicorn**: ASGI server

## Document Processing
- **PyPDF2**: PDF document processing
- **python-docx**: Word document processing
- **textract**: Multi-format document extraction
- **lxml**: XML/HTML processing
- **pyyaml**: YAML configuration files

## Development Tools
- **docker-compose**: Container orchestration
- **python-dotenv**: Environment variable management
- **psutil**: System monitoring
- **schedule**: Task scheduling
- **shutup**: Warning suppression

## Graph and Visualization
- **graphdatascience**: Neo4j Graph Data Science
- **pyvis**: Network visualization
- **matplotlib**: Plotting and charts
- **networkx**: Graph analysis

## Chinese Language Support
- **jieba**: Chinese text segmentation
- **hanlp**: Advanced Chinese NLP

## External Services
- **One-API**: API proxy for multiple LLM providers
- **LangSmith**: LLM monitoring and tracing (optional)