# Suggested Commands for Development

## Environment Setup
```bash
# Create conda environment
conda create -n graphrag python==3.10
conda activate graphrag

# Install dependencies
cd graph-rag-agent/
pip install -r requirements.txt
pip install -e .
```

## Infrastructure Setup
```bash
# Start Neo4j database
docker compose up -d

# Start One-API proxy (optional)
docker run --name one-api -d --restart always \
  -p 13000:3000 \
  -e TZ=Asia/Shanghai \
  -v /home/<USER>/data/one-api:/data \
  justsong/one-api
```

## Configuration
```bash
# Copy environment template
cp .env.example .env
# Edit .env with your API keys and configuration
```

## Knowledge Graph Building
```bash
# Build complete knowledge graph from documents in files/
cd graph-rag-agent/
python build/main.py

# Or build components separately:
python -m build.build_graph
python -m build.build_index_and_community
python -m build.build_chunk_index
```

## Running the Application
```bash
# Start backend server
cd graph-rag-agent/
python server/main.py

# Start frontend (in another terminal)
cd graph-rag-agent/
streamlit run frontend/app.py
```

## Testing
```bash
# Test different agents with streaming
cd graph-rag-agent/
python test/search_with_stream.py

# Test without streaming
python test/search_without_stream.py

# Test specific components
python test/test_cache_system.py
python test/test_deep_agent.py
```

## Development Utilities
```bash
# Check system processes
ps aux | grep python

# Monitor Neo4j
docker logs neo4j

# Check application logs
tail -f logs/app.log  # if logging to file

# Clean cache
rm -rf cache/
rm -rf __pycache__/
```

## macOS Specific Commands
```bash
# Install system dependencies for document processing
brew install poppler antiword unrtf

# Check Python processes
ps aux | grep python | grep -v grep

# Monitor file changes
fswatch -o . | xargs -n1 -I{} echo "Files changed"

# Network monitoring
lsof -i :8000  # Check what's using port 8000
lsof -i :7687  # Check Neo4j port
```

## Git Operations
```bash
# Standard git workflow
git status
git add .
git commit -m "feat: description"
git push origin main

# Check repository status
git log --oneline -10
git branch -a
```

## Debugging
```bash
# Python debugging
python -m pdb script.py

# Check imports
python -c "import module_name; print(module_name.__file__)"

# Environment debugging
python -c "import sys; print(sys.path)"
python -c "import os; print(os.environ.get('OPENAI_API_KEY', 'Not set'))"
```