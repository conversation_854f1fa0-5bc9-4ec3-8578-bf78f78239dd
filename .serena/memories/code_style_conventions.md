# Code Style and Conventions

## General Style
- **Language**: Python 3.10+
- **Encoding**: UTF-8
- **Line Endings**: Unix-style (LF)
- **Indentation**: 4 spaces (no tabs)

## Naming Conventions
- **Files/Modules**: snake_case (e.g., `agent_coordinator.py`, `build_graph.py`)
- **Classes**: PascalCase (e.g., `BaseAgent`, `KnowledgeGraphBuilder`)
- **Functions/Methods**: snake_case (e.g., `process_all()`, `get_llm_model()`)
- **Variables**: snake_case (e.g., `cache_dir`, `db_manager`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_TOKENS`, `NEO4J_URI`)

## Documentation
- **Docstrings**: Chinese language docstrings are used throughout
- **Format**: Google-style docstrings with Chinese descriptions
- **Comments**: Mix of Chinese and English, with Chinese for business logic explanations

## Type Hints
- **Usage**: Extensive use of type hints from `typing` module
- **Imports**: `from typing import List, Dict, Any, Optional, Union`
- **Return Types**: Always specify return types for functions
- **Complex Types**: Use TypedDict for structured data

## Import Organization
- **Standard Library**: First group
- **Third-party**: Second group (langchain, fastapi, etc.)
- **Local Imports**: Last group (relative imports)
- **Style**: Absolute imports preferred, relative imports for local modules

## Error Handling
- **Exceptions**: Use specific exception types
- **Logging**: Rich console output for user-facing messages
- **Graceful Degradation**: Fallback mechanisms for component failures

## Async/Await
- **Usage**: Extensive use of async/await for I/O operations
- **Patterns**: AsyncGenerator for streaming responses
- **Context Managers**: `async with` for resource management

## Configuration
- **Environment Variables**: Used for all external service configuration
- **Settings**: Centralized in `config/settings.py`
- **Secrets**: Stored in `.env` file (not committed)

## Architecture Patterns
- **Abstract Base Classes**: Used for defining interfaces (e.g., `BaseAgent`)
- **Factory Pattern**: For creating different types of agents and tools
- **Strategy Pattern**: For different search and caching strategies
- **Dependency Injection**: Configuration and services injected into classes