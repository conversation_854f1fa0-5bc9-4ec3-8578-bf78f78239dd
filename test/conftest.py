"""
Pytest configuration and fixtures for MCP integration tests

This module provides shared fixtures and configuration for all MCP integration tests.
"""

import pytest
import asyncio
import os
import tempfile
import shutil
from typing import AsyncGenerator, Dict, Any
from unittest.mock import AsyncMock, MagicMock
from fastapi import FastAPI
from fastapi.testclient import TestClient
from fastmcp import FastMCP
from fastmcp.server.http import create_sse_app, create_streamable_http_app


# Test configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "streaming: mark test as streaming functionality test"
    )
    config.addinivalue_line(
        "markers", "mcp_tools: mark test as MCP tools integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running test"
    )


# Async test configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Test environment fixtures
@pytest.fixture(scope="session")
def test_env_vars():
    """Set up test environment variables"""
    test_vars = {
        "OPENAI_API_KEY": "test-api-key",
        "NEO4J_URI": "bolt://localhost:7687",
        "NEO4J_USERNAME": "neo4j",
        "NEO4J_PASSWORD": "test-password",
        "CACHE_DIR": "./test_cache",
        "LOG_LEVEL": "DEBUG"
    }
    
    # Store original values
    original_vars = {}
    for key, value in test_vars.items():
        original_vars[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield test_vars
    
    # Restore original values
    for key, original_value in original_vars.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture
def temp_cache_dir():
    """Create temporary cache directory for tests"""
    temp_dir = tempfile.mkdtemp(prefix="mcp_test_cache_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


# Mock fixtures for external dependencies
@pytest.fixture
def mock_llm():
    """Mock LLM for testing"""
    mock = AsyncMock()
    mock.ainvoke.return_value = "Mock LLM response"
    mock.astream.return_value = AsyncMock()
    return mock


@pytest.fixture
def mock_embeddings():
    """Mock embeddings model for testing"""
    mock = AsyncMock()
    mock.aembed_query.return_value = [0.1, 0.2, 0.3, 0.4, 0.5]
    mock.aembed_documents.return_value = [[0.1, 0.2, 0.3, 0.4, 0.5]]
    return mock


@pytest.fixture
def mock_neo4j_driver():
    """Mock Neo4j driver for testing"""
    mock_driver = MagicMock()
    mock_session = MagicMock()
    mock_result = MagicMock()
    
    # Configure mock behavior
    mock_result.data.return_value = [
        {"node": {"name": "test_node", "type": "concept"}},
        {"relationship": {"type": "RELATED_TO", "strength": 0.8}}
    ]
    
    mock_session.run.return_value = mock_result
    mock_driver.session.return_value.__enter__.return_value = mock_session
    mock_driver.session.return_value.__exit__.return_value = None
    
    return mock_driver


# MCP server fixtures
@pytest.fixture
async def basic_mcp_server():
    """Create basic MCP server for testing"""
    app = FastMCP("GraphRAG-Test-Basic")
    
    @app.tool()
    async def test_tool(message: str) -> str:
        """Basic test tool"""
        return f"Test response: {message}"
    
    @app.tool()
    async def async_test_tool(delay: float = 0.1) -> str:
        """Async test tool with delay"""
        await asyncio.sleep(delay)
        return f"Async response after {delay}s"
    
    return app


@pytest.fixture
async def streaming_mcp_server():
    """Create MCP server with streaming capabilities"""
    app = FastMCP("GraphRAG-Test-Streaming")
    
    @app.tool()
    async def stream_numbers(count: int = 5) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream a sequence of numbers"""
        for i in range(count):
            yield {
                "number": i,
                "timestamp": f"2024-01-01T10:0{i}:00Z",
                "message": f"Number {i}"
            }
            await asyncio.sleep(0.1)
    
    @app.tool()
    async def stream_search_simulation(query: str, max_results: int = 10) -> AsyncGenerator[Dict[str, Any], None]:
        """Simulate streaming search results"""
        for i in range(min(max_results, 5)):
            yield {
                "result_id": f"result_{i}",
                "content": f"Search result {i} for query: {query}",
                "relevance_score": 1.0 - (i * 0.1),
                "timestamp": f"2024-01-01T10:0{i}:00Z"
            }
            await asyncio.sleep(0.05)
    
    return app


@pytest.fixture
def fastapi_with_mcp(basic_mcp_server):
    """Create FastAPI app with MCP integration"""
    # Create SSE app for MCP integration
    mcp_app = create_sse_app(basic_mcp_server)

    # Add basic health check endpoint
    @mcp_app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "GraphRAG-Test"}

    return mcp_app


@pytest.fixture
def test_client(fastapi_with_mcp):
    """Create test client for FastAPI app"""
    return TestClient(fastapi_with_mcp)


# Mock agent fixtures
@pytest.fixture
def mock_base_agent():
    """Mock base agent for testing"""
    mock_agent = AsyncMock()
    mock_agent.process_query.return_value = {
        "answer": "Mock agent response",
        "sources": ["source1", "source2"],
        "confidence": 0.85
    }
    mock_agent.stream_process_query.return_value = AsyncMock()
    return mock_agent


@pytest.fixture
def mock_agent_coordinator():
    """Mock agent coordinator for testing"""
    mock_coordinator = AsyncMock()
    mock_coordinator.coordinate_agents.return_value = {
        "coordinated_result": "Mock coordination result",
        "participating_agents": ["agent1", "agent2"],
        "coordination_strategy": "parallel"
    }
    return mock_coordinator


# Mock search tool fixtures
@pytest.fixture
def mock_local_search_tool():
    """Mock local search tool"""
    mock_tool = AsyncMock()
    mock_tool.search.return_value = {
        "results": [
            {"id": "local_1", "content": "Local result 1", "score": 0.9},
            {"id": "local_2", "content": "Local result 2", "score": 0.8}
        ],
        "search_type": "local",
        "execution_time": 0.5
    }
    return mock_tool


@pytest.fixture
def mock_global_search_tool():
    """Mock global search tool"""
    mock_tool = AsyncMock()
    mock_tool.search.return_value = {
        "communities": [
            {"id": "comm_1", "summary": "Community 1 summary", "score": 0.85},
            {"id": "comm_2", "summary": "Community 2 summary", "score": 0.75}
        ],
        "search_type": "global",
        "execution_time": 1.2
    }
    return mock_tool


# Mock cache fixtures
@pytest.fixture
def mock_cache_manager():
    """Mock cache manager for testing"""
    cache_storage = {}
    
    class MockCacheManager:
        def __init__(self):
            self.storage = cache_storage
            self.stats = {"hits": 0, "misses": 0, "sets": 0}
        
        async def get(self, key: str):
            if key in self.storage:
                self.stats["hits"] += 1
                return self.storage[key]
            else:
                self.stats["misses"] += 1
                return None
        
        async def set(self, key: str, value: Any, ttl: int = None):
            self.storage[key] = value
            self.stats["sets"] += 1
            return True
        
        async def invalidate(self, pattern: str):
            invalidated = []
            for key in list(self.storage.keys()):
                if pattern in key or pattern == "*":
                    del self.storage[key]
                    invalidated.append(key)
            return invalidated
        
        def get_stats(self):
            return self.stats.copy()
    
    return MockCacheManager()


# Test data fixtures
@pytest.fixture
def sample_query_data():
    """Sample query data for testing"""
    return {
        "simple_queries": [
            "What is artificial intelligence?",
            "Define machine learning",
            "What is Python?"
        ],
        "complex_queries": [
            "How do machine learning algorithms impact healthcare outcomes and what are the ethical considerations?",
            "Analyze the relationship between climate change and economic policy in developing countries",
            "Compare and contrast different approaches to natural language processing in the context of multilingual applications"
        ],
        "graph_queries": [
            "How are artificial intelligence and healthcare connected?",
            "What is the relationship between machine learning and data science?",
            "Show me the connections between renewable energy and economic policy"
        ]
    }


@pytest.fixture
def sample_search_results():
    """Sample search results for testing"""
    return {
        "local_results": [
            {
                "id": "local_1",
                "content": "Local search result about AI applications in healthcare",
                "score": 0.95,
                "source": "document_1.pdf",
                "community_id": "healthcare_ai"
            },
            {
                "id": "local_2", 
                "content": "Local search result about machine learning algorithms",
                "score": 0.87,
                "source": "document_2.pdf",
                "community_id": "ml_algorithms"
            }
        ],
        "global_results": [
            {
                "community_id": "healthcare_ai",
                "summary": "Community focused on AI applications in healthcare sector",
                "relevance_score": 0.92,
                "entities": ["artificial_intelligence", "healthcare", "medical_diagnosis"],
                "relationships": ["AI_USED_IN_healthcare", "healthcare_BENEFITS_FROM_AI"]
            },
            {
                "community_id": "ml_algorithms",
                "summary": "Community about machine learning algorithms and techniques",
                "relevance_score": 0.88,
                "entities": ["machine_learning", "algorithms", "neural_networks"],
                "relationships": ["ML_INCLUDES_algorithms", "algorithms_IMPLEMENT_neural_networks"]
            }
        ]
    }


# Performance testing fixtures
@pytest.fixture
def performance_monitor():
    """Performance monitoring fixture"""
    import time
    
    class PerformanceMonitor:
        def __init__(self):
            self.metrics = {}
        
        def start_timer(self, operation: str):
            self.metrics[operation] = {"start": time.time()}
        
        def end_timer(self, operation: str):
            if operation in self.metrics:
                self.metrics[operation]["end"] = time.time()
                self.metrics[operation]["duration"] = (
                    self.metrics[operation]["end"] - self.metrics[operation]["start"]
                )
        
        def get_duration(self, operation: str):
            return self.metrics.get(operation, {}).get("duration", 0)
        
        def get_all_metrics(self):
            return self.metrics.copy()
    
    return PerformanceMonitor()


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test"""
    yield
    # Cleanup code here if needed
    pass


# Test markers and parametrization helpers
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names"""
    for item in items:
        # Add slow marker to tests that might be slow
        if "performance" in item.name or "load" in item.name or "stress" in item.name:
            item.add_marker(pytest.mark.slow)
        
        # Add integration marker to integration tests
        if "integration" in item.name or "test_mcp" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add streaming marker to streaming tests
        if "stream" in item.name or "sse" in item.name:
            item.add_marker(pytest.mark.streaming)


# Custom assertions
def assert_mcp_tool_response(response, expected_fields=None):
    """Custom assertion for MCP tool responses"""
    if expected_fields is None:
        expected_fields = ["status", "result"]
    
    assert isinstance(response, dict), "Response should be a dictionary"
    
    for field in expected_fields:
        assert field in response, f"Response should contain '{field}' field"


def assert_streaming_response(stream_items, min_items=1, required_fields=None):
    """Custom assertion for streaming responses"""
    if required_fields is None:
        required_fields = ["timestamp"]
    
    assert len(stream_items) >= min_items, f"Should have at least {min_items} stream items"
    
    for item in stream_items:
        assert isinstance(item, dict), "Each stream item should be a dictionary"
        for field in required_fields:
            assert field in item, f"Stream item should contain '{field}' field"


# Test utilities
class TestUtils:
    """Utility functions for tests"""
    
    @staticmethod
    async def collect_stream_items(async_generator, max_items=10):
        """Collect items from async generator for testing"""
        items = []
        async for item in async_generator:
            items.append(item)
            if len(items) >= max_items:
                break
        return items
    
    @staticmethod
    def create_mock_stream_response(items):
        """Create mock streaming response"""
        async def mock_stream():
            for item in items:
                yield item
                await asyncio.sleep(0.01)
        return mock_stream()
    
    @staticmethod
    def validate_json_serializable(obj):
        """Validate that object is JSON serializable"""
        import json
        try:
            json.dumps(obj)
            return True
        except (TypeError, ValueError):
            return False


@pytest.fixture
def test_utils():
    """Provide test utilities"""
    return TestUtils