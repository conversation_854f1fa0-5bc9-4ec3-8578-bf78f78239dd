#!/usr/bin/env python3
"""
MCP Integration Test Runner

This script runs the MCP integration tests following TDD methodology.
It provides different test execution modes and detailed reporting.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Any


def setup_test_environment():
    """Setup test environment and dependencies"""
    print("🔧 Setting up test environment...")
    
    # Add project root to Python path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    # Set test environment variables
    test_env = {
        "PYTHONPATH": str(project_root),
        "TESTING": "true",
        "LOG_LEVEL": "DEBUG",
        "CACHE_DIR": "./test_cache",
        "NEO4J_URI": "bolt://localhost:7687",
        "NEO4J_USERNAME": "neo4j",
        "NEO4J_PASSWORD": "test-password",
        "OPENAI_API_KEY": "test-api-key"
    }
    
    for key, value in test_env.items():
        os.environ[key] = value
    
    print("✅ Test environment configured")
    return test_env


def install_test_dependencies():
    """Install test dependencies"""
    print("📦 Installing test dependencies...")
    
    test_requirements = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-mock>=3.10.0",
        "pytest-cov>=4.0.0",
        "pytest-html>=3.1.0",
        "pytest-xdist>=3.0.0",  # For parallel test execution
        "fastmcp>=0.1.0",  # MCP library
        "httpx>=0.24.0",  # For async HTTP testing
        "websockets>=11.0.0"  # For WebSocket testing
    ]
    
    for requirement in test_requirements:
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", requirement
            ], check=True, capture_output=True)
            print(f"  ✅ Installed {requirement}")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install {requirement}: {e}")
            return False
    
    print("✅ All test dependencies installed")
    return True


def run_pytest_command(args: List[str], description: str) -> bool:
    """Run pytest command with given arguments"""
    print(f"\n🧪 {description}")
    print(f"Command: pytest {' '.join(args)}")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest"
        ] + args, check=False, capture_output=False)
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED (exit code: {result.returncode})")
            return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False


def run_basic_tests():
    """Run basic MCP integration tests"""
    args = [
        "test/test_mcp_integration.py",
        "-v",
        "--tb=short",
        "-x"  # Stop on first failure
    ]
    return run_pytest_command(args, "Basic MCP Integration Tests")


def run_streaming_tests():
    """Run streaming functionality tests"""
    args = [
        "test/test_mcp_streaming.py",
        "-v",
        "--tb=short",
        "-m", "streaming"
    ]
    return run_pytest_command(args, "MCP Streaming Tests")


def run_tools_tests():
    """Run MCP tools integration tests"""
    args = [
        "test/test_mcp_tools.py",
        "-v",
        "--tb=short",
        "-m", "mcp_tools"
    ]
    return run_pytest_command(args, "MCP Tools Integration Tests")


def run_performance_tests():
    """Run performance tests"""
    args = [
        "test/",
        "-v",
        "--tb=short",
        "-m", "performance",
        "--durations=10"  # Show 10 slowest tests
    ]
    return run_pytest_command(args, "MCP Performance Tests")


def run_all_tests():
    """Run all MCP integration tests"""
    args = [
        "test/",
        "-v",
        "--tb=short",
        "--cov=.",
        "--cov-report=html:test_reports/coverage",
        "--cov-report=term-missing",
        "--html=test_reports/report.html",
        "--self-contained-html",
        "-m", "not slow"  # Exclude slow tests by default
    ]
    return run_pytest_command(args, "All MCP Integration Tests")


def run_parallel_tests():
    """Run tests in parallel"""
    args = [
        "test/",
        "-v",
        "--tb=short",
        "-n", "auto",  # Auto-detect number of CPUs
        "--dist=worksteal",
        "-m", "not slow"
    ]
    return run_pytest_command(args, "Parallel MCP Tests")


def run_specific_test_class(test_class: str):
    """Run specific test class"""
    args = [
        "test/",
        "-v",
        "--tb=short",
        "-k", test_class
    ]
    return run_pytest_command(args, f"Test Class: {test_class}")


def run_tdd_cycle(test_pattern: str = None):
    """Run TDD cycle - tests should fail initially"""
    print("\n🔄 Running TDD Cycle")
    
    if test_pattern:
        args = [
            "test/",
            "-v",
            "--tb=short",
            "-k", test_pattern,
            "--maxfail=1"
        ]
        description = f"TDD Cycle for pattern: {test_pattern}"
    else:
        args = [
            "test/test_mcp_integration.py",
            "-v",
            "--tb=short",
            "--maxfail=5"
        ]
        description = "TDD Cycle - Basic Integration Tests"
    
    print("Expected: Some tests should fail (TDD Red phase)")
    result = run_pytest_command(args, description)
    
    if result:
        print("⚠️  All tests passed - Implementation may already exist")
    else:
        print("✅ Tests failed as expected - Ready for implementation (TDD Red phase)")
    
    return not result  # Return True if tests failed (expected in TDD)


def create_test_reports_directory():
    """Create directory for test reports"""
    reports_dir = Path("test_reports")
    reports_dir.mkdir(exist_ok=True)
    print(f"📊 Test reports will be saved to: {reports_dir.absolute()}")


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="MCP Integration Test Runner")
    parser.add_argument(
        "mode",
        choices=[
            "setup", "basic", "streaming", "tools", "performance", 
            "all", "parallel", "tdd", "class", "pattern"
        ],
        help="Test execution mode"
    )
    parser.add_argument(
        "--target",
        help="Target for class or pattern mode (test class name or pattern)"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies before running tests"
    )
    parser.add_argument(
        "--no-setup",
        action="store_true",
        help="Skip test environment setup"
    )
    
    args = parser.parse_args()
    
    print("🚀 MCP Integration Test Runner")
    print("=" * 50)
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_test_dependencies():
            print("❌ Failed to install dependencies")
            return 1
    
    # Setup test environment
    if not args.no_setup:
        setup_test_environment()
    
    # Create test reports directory
    create_test_reports_directory()
    
    # Run tests based on mode
    success = True
    
    if args.mode == "setup":
        print("✅ Test environment setup completed")
        
    elif args.mode == "basic":
        success = run_basic_tests()
        
    elif args.mode == "streaming":
        success = run_streaming_tests()
        
    elif args.mode == "tools":
        success = run_tools_tests()
        
    elif args.mode == "performance":
        success = run_performance_tests()
        
    elif args.mode == "all":
        success = run_all_tests()
        
    elif args.mode == "parallel":
        success = run_parallel_tests()
        
    elif args.mode == "tdd":
        success = run_tdd_cycle(args.target)
        
    elif args.mode == "class":
        if not args.target:
            print("❌ --target required for class mode")
            return 1
        success = run_specific_test_class(args.target)
        
    elif args.mode == "pattern":
        if not args.target:
            print("❌ --target required for pattern mode")
            return 1
        success = run_tdd_cycle(args.target)
    
    # Print summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test execution completed successfully!")
    else:
        print("💥 Test execution failed!")
    
    print(f"📊 Test reports available in: test_reports/")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())