#!/usr/bin/env python3
"""
STDIO MCP Server for Claude Desktop Integration

This script provides STDIO transport for the GraphRAG MCP server,
enabling integration with <PERSON> Deskt<PERSON> and other STDIO-based MCP clients.

Usage:
    python run_mcp_stdio.py

For Claude Desktop, add this to your configuration:
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python",
      "args": ["/path/to/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/path/to/graph-rag-agent"
      }
    }
  }
}
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mcp_integration.config import MCPConfig
from mcp_integration.server import GraphRAGMCPServer


async def run_stdio_server():
    """Run the MCP server with STDIO transport for Claude Desktop integration"""
    
    # Configure logging for STDIO (send to stderr to avoid interfering with STDIO protocol)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stderr  # Important: use stderr for logging in STDIO mode
    )
    
    logger = logging.getLogger("GraphRAG-MCP-STDIO")
    logger.info("Starting GraphRAG MCP Server with STDIO transport...")
    
    try:
        # Create MCP configuration for STDIO transport
        config = MCPConfig(
            server_name="GraphRAG-DeepSearch-MCP-STDIO",
            host="localhost",  # Not used for STDIO but required for config
            port=0,  # Not used for STDIO
            enable_streaming=True,
            enable_agent_coordination=True,
            enable_distributed_cache=True,
            enable_performance_monitoring=True,
            log_level="INFO"
        )
        
        # Create MCP server instance
        mcp_server = GraphRAGMCPServer(config)
        
        # Get the FastMCP app
        mcp_app = mcp_server.get_mcp_app()
        
        logger.info("✅ MCP server initialized successfully")
        logger.info("🔌 Starting STDIO transport...")
        
        # Start background tasks
        background_tasks = await mcp_server.start_background_tasks()
        logger.info(f"✅ Started {len(background_tasks)} background tasks")
        
        # Run the MCP server with STDIO transport
        # This will handle stdin/stdout communication with Claude Desktop
        await mcp_app.run_async(transport="stdio")
        
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal, shutting down...")
    except Exception as e:
        logger.error(f"❌ Error running STDIO server: {e}")
        raise
    finally:
        logger.info("🔚 GraphRAG MCP STDIO server stopped")


def main():
    """Main entry point for STDIO MCP server"""
    try:
        # Run the async server
        asyncio.run(run_stdio_server())
    except KeyboardInterrupt:
        print("Server interrupted", file=sys.stderr)
        sys.exit(0)
    except Exception as e:
        print(f"Server error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
