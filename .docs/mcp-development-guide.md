# GraphRAG MCP Development Guide

## Development Environment Setup

### Prerequisites

- Python 3.8+
- Cursor IDE or VS Code
- Git
- Virtual environment (recommended)

### Initial Setup

1. **Clone and Setup**
```bash
git clone <repository>
cd graph-rag-agent
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Install MCP Dependencies**
```bash
python setup_mcp_cursor.py
```

3. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

## Development Workflow

### 1. TDD (Test-Driven Development)

The MCP integration follows TDD methodology:

```bash
# Run tests in TDD mode
python test/run_mcp_tests.py tdd

# Run specific test categories
python test/run_mcp_tests.py basic
python test/run_mcp_tests.py streaming
python test/run_mcp_tests.py tools
```

### 2. Development Server

Start the development server with auto-reload:

```bash
# Development mode with auto-reload
python run_mcp_server.py --reload

# Or use Cursor task: "Start MCP Server"
```

### 3. Testing Changes

```bash
# Test client functionality
python test_mcp_client.py

# Run full test suite
python test/run_mcp_tests.py all

# Run performance tests
python test/run_mcp_tests.py performance
```

## Adding New Tools

### 1. Create Tool Module

Create a new tool in `mcp_integration/tools/`:

```python
# mcp_integration/tools/my_new_tool.py
from .base import BaseToolManager
from fastmcp import FastMCP

class MyNewToolManager(BaseToolManager):
    def __init__(self, config):
        super().__init__(config)
        # Tool-specific initialization
    
    def register_tools(self, mcp_app: FastMCP):
        if not self.enabled:
            return
        
        @mcp_app.tool()
        async def my_new_tool(param1: str, param2: int = 10):
            """Description of my new tool"""
            try:
                # Tool implementation
                result = {"param1": param1, "param2": param2}
                return result
            except Exception as e:
                return self._handle_tool_error("my_new_tool", e)
        
        # Track registered tools
        self.registered_tools["my_new_tool"] = my_new_tool
```

### 2. Register Tool Manager

Update `mcp_integration/tools/__init__.py`:

```python
from .my_new_tool import MyNewToolManager

__all__ = [
    # ... existing tools
    "MyNewToolManager"
]
```

### 3. Integrate with Server

Update `mcp_integration/server.py`:

```python
from .tools import MyNewToolManager

def _initialize_tool_managers(self):
    # ... existing tool managers
    
    if "my_new_tool" in self.config.enabled_tool_categories:
        self.tool_managers["my_new_tool"] = MyNewToolManager(
            self.config.get_tool_config("my_new_tool")
        )
```

### 4. Add Configuration

Update `mcp_integration/config.py`:

```python
enabled_tool_categories: List[str] = field(default_factory=lambda: [
    # ... existing categories
    "my_new_tool"
])
```

### 5. Write Tests

Create tests in `test/test_my_new_tool.py`:

```python
import pytest
from mcp_integration.tools.my_new_tool import MyNewToolManager

class TestMyNewTool:
    @pytest.fixture
    def tool_manager(self):
        config = {"enabled": True}
        return MyNewToolManager(config)
    
    @pytest.mark.asyncio
    async def test_my_new_tool(self, tool_manager):
        # Test implementation
        pass
```

## Streaming Tools Development

### 1. Streaming Tool Template

```python
@mcp_app.tool()
async def my_streaming_tool(
    param: str
) -> AsyncGenerator[Dict[str, Any], None]:
    """Streaming tool that yields results progressively"""
    try:
        # Initialize
        yield {
            "event_type": "started",
            "param": param,
            "timestamp": time.time()
        }
        
        # Process and yield results
        for i in range(5):
            await asyncio.sleep(0.1)  # Simulate processing
            yield {
                "event_type": "progress",
                "step": i + 1,
                "data": f"Result {i}",
                "timestamp": time.time()
            }
        
        # Complete
        yield {
            "event_type": "completed",
            "total_results": 5,
            "timestamp": time.time()
        }
        
    except Exception as e:
        yield self._handle_tool_error("my_streaming_tool", e)
```

### 2. Client-Side Streaming

```python
async def consume_streaming_tool():
    async with mcp_client_context("http://localhost:8001") as client:
        async for update in client.stream_tool_response("my_streaming_tool", param="test"):
            if update.get("error"):
                print(f"Error: {update['message']}")
                break
            
            event_type = update.get("event_type")
            if event_type == "progress":
                print(f"Progress: {update['step']}/5 - {update['data']}")
            elif event_type == "completed":
                print(f"Completed: {update['total_results']} results")
```

## Configuration Management

### Environment Variables

```bash
# Server Configuration
MCP_SERVER_NAME="My-Custom-MCP"
MCP_HOST="localhost"
MCP_PORT="8001"

# Tool Configuration
MCP_ENABLED_TOOL_CATEGORIES="agent_coordination,search_tools,my_new_tool"

# Feature Flags
MCP_ENABLE_STREAMING="true"
MCP_ENABLE_PERFORMANCE_MONITORING="true"

# Logging
MCP_LOG_LEVEL="DEBUG"
MCP_LOG_REQUESTS="true"
```

### Programmatic Configuration

```python
from mcp_integration.config import MCPConfig

config = MCPConfig(
    server_name="Custom-MCP-Server",
    enable_streaming=True,
    enabled_tool_categories=["my_new_tool"],
    performance_alert_thresholds={
        "response_time_warning": 1.0,
        "response_time_critical": 3.0
    }
)
```

## Debugging and Troubleshooting

### 1. Enable Debug Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Or set environment variable
export MCP_LOG_LEVEL="DEBUG"
```

### 2. Debug Streaming Issues

```python
# Add debug prints in streaming tools
async def debug_streaming_tool():
    print("Starting stream...")
    yield {"debug": "stream_started"}
    
    for i in range(3):
        print(f"Yielding item {i}")
        yield {"item": i, "debug": f"item_{i}"}
        await asyncio.sleep(0.1)
    
    print("Stream completed")
    yield {"debug": "stream_completed"}
```

### 3. Test Individual Tools

```python
# Test tool directly without MCP server
from mcp_integration.tools.my_tool import MyToolManager

async def test_tool_directly():
    config = {"enabled": True}
    tool_manager = MyToolManager(config)
    
    # Create mock MCP app
    from fastmcp import FastMCP
    app = FastMCP("test")
    tool_manager.register_tools(app)
    
    # Test tool
    result = await app.tools["my_tool"].func(param="test")
    print(result)
```

### 4. Monitor Performance

```python
import time

async def performance_test():
    start_time = time.time()
    
    async with mcp_client_context("http://localhost:8001") as client:
        result = await client.call_tool("my_tool", param="test")
    
    duration = time.time() - start_time
    print(f"Tool call took {duration:.3f} seconds")
```

## Best Practices

### 1. Error Handling

```python
async def robust_tool(param: str):
    try:
        # Validate input
        if not param or len(param) < 3:
            raise ValueError("Parameter must be at least 3 characters")
        
        # Process
        result = process_param(param)
        
        return {"success": True, "result": result}
        
    except ValueError as e:
        return {"error": True, "message": str(e), "type": "validation_error"}
    except Exception as e:
        logger.error(f"Unexpected error in robust_tool: {e}")
        return {"error": True, "message": "Internal error", "type": "internal_error"}
```

### 2. Resource Management

```python
class ResourceManagedTool(BaseToolManager):
    def __init__(self, config):
        super().__init__(config)
        self.connections = []
        self.background_tasks = []
    
    async def start(self):
        # Initialize resources
        pass
    
    async def stop(self):
        # Cleanup resources
        for task in self.background_tasks:
            task.cancel()
        
        for conn in self.connections:
            await conn.close()
```

### 3. Testing Patterns

```python
@pytest.mark.asyncio
async def test_tool_with_mock():
    with patch('my_module.external_service') as mock_service:
        mock_service.return_value = {"mocked": "data"}
        
        result = await my_tool("test_param")
        
        assert result["success"] is True
        mock_service.assert_called_once_with("test_param")
```

### 4. Documentation

```python
@mcp_app.tool()
async def well_documented_tool(
    query: str,
    max_results: int = 10,
    include_metadata: bool = False
) -> Dict[str, Any]:
    """
    Execute a well-documented tool operation.
    
    Args:
        query: The search query string
        max_results: Maximum number of results to return (1-100)
        include_metadata: Whether to include result metadata
    
    Returns:
        Dictionary containing:
        - results: List of search results
        - total_count: Total number of results found
        - execution_time: Time taken to execute
        - metadata: Additional metadata (if requested)
    
    Raises:
        ValueError: If query is empty or max_results is invalid
    """
    # Implementation here
```

## Deployment

### Development Deployment

```bash
# Start development server
python run_mcp_server.py

# With auto-reload
uvicorn mcp_integration.server:app --reload --host localhost --port 8001
```

### Production Deployment

```bash
# Using gunicorn
gunicorn mcp_integration.server:app -w 4 -k uvicorn.workers.UvicornWorker

# Using Docker
docker build -t graphrag-mcp .
docker run -p 8001:8001 graphrag-mcp
```

### Environment-Specific Configuration

```python
# config/development.py
MCP_LOG_LEVEL = "DEBUG"
MCP_ENABLE_AUTO_RELOAD = True

# config/production.py
MCP_LOG_LEVEL = "INFO"
MCP_ENABLE_PERFORMANCE_MONITORING = True
MCP_ENABLE_AUTHENTICATION = True
```

This development guide provides the foundation for extending and maintaining the GraphRAG MCP integration. Follow these patterns and practices to ensure consistent, reliable, and maintainable code.
