# GraphRAG MCP Architecture

## Overview

The GraphRAG MCP (Model Context Protocol) integration provides a real-time communication layer for the GraphRAG + DeepSearch system. This document describes the architecture, components, and design decisions.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           GraphRAG + DeepSearch System                      │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │   Client Apps   │    │   MCP Server    │    │   GraphRAG      │         │
│  │                 │    │                 │    │   Backend       │         │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│  │ │   Cursor    │◄┼────┼►│   FastMCP   │◄┼────┼►│   Agents    │ │         │
│  │ │   IDE       │ │    │ │   Server    │ │    │ │   Manager   │ │         │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │         │
│  │                 │    │                 │    │                 │         │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│  │ │   Web UI    │◄┼────┼►│  Streaming  │◄┼────┼►│   Search    │ │         │
│  │ │ (Streamlit) │ │    │ │  Manager    │ │    │ │   Engine    │ │         │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │         │
│  │                 │    │                 │    │                 │         │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│  │ │   Custom    │◄┼────┼►│    Tools    │◄┼────┼►│    Cache    │ │         │
│  │ │   Clients   │ │    │ │   Manager   │ │    │ │   Manager   │ │         │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │         │
│  └─────────────────┘    │                 │    │                 │         │
│                         │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│                         │ │ Middleware  │◄┼────┼►│ Knowledge   │ │         │
│                         │ │   Stack     │ │    │ │   Graph     │ │         │
│                         │ └─────────────┘ │    │ └─────────────┘ │         │
│                         └─────────────────┘    └─────────────────┘         │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                              Transport Layer                                │
│  HTTP/REST  │  WebSockets  │  Server-Sent Events  │  FastAPI Integration   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. MCP Server (`mcp_integration/server.py`)

The central component that orchestrates all MCP functionality.

**Key Features:**
- FastMCP integration with SSE support
- Tool registration and management
- Background task coordination
- FastAPI integration for existing applications

**Responsibilities:**
- Register and manage MCP tools
- Handle client connections
- Coordinate streaming responses
- Manage server lifecycle

### 2. Streaming Manager (`mcp_integration/streaming.py`)

Handles real-time streaming capabilities using Server-Sent Events (SSE).

**Key Features:**
- Session management
- Multiple stream types
- Backpressure handling
- Connection cleanup

**Stream Types:**
- Agent coordination updates
- Search result streaming
- Knowledge graph exploration
- Performance metrics
- Cache invalidation events

### 3. Tool Managers (`mcp_integration/tools/`)

Modular tool implementations organized by functionality.

#### Agent Coordination Tools
- Multi-agent coordination with streaming updates
- Role assignment and optimization
- Real-time agent state broadcasting
- Adaptive coordination strategies

#### Search Tools
- Local vector search
- Global community search
- Hybrid search approaches
- Real-time result streaming
- Search progress tracking

#### Cache Tools
- Distributed cache management
- Real-time invalidation
- Performance monitoring
- Cache synchronization

#### Knowledge Graph Tools
- Interactive graph exploration
- Node expansion and traversal
- Path finding algorithms
- Real-time graph updates

#### Performance Monitoring Tools
- System metrics collection
- Application performance tracking
- Real-time alerting
- Comprehensive reporting

### 4. MCP Client (`mcp_integration/client.py`)

Client library for consuming MCP services.

**Features:**
- Async/await support
- Streaming response handling
- Connection management
- Context manager support
- Convenience methods

### 5. Configuration System (`mcp_integration/config.py`)

Centralized configuration management.

**Features:**
- Environment variable support
- Validation and defaults
- Tool-specific configuration
- Runtime configuration updates

### 6. Middleware Stack (`mcp_integration/middleware.py`)

Request/response processing pipeline.

**Components:**
- Authentication middleware
- Logging and monitoring
- Performance tracking
- Error handling
- Rate limiting
- Input validation

## Design Principles

### 1. Modularity

Each tool category is implemented as a separate module with its own:
- Configuration
- State management
- Error handling
- Testing

### 2. Streaming-First

All tools support streaming where applicable:
- Real-time updates
- Progressive results
- Backpressure handling
- Connection management

### 3. Backward Compatibility

The MCP integration maintains compatibility with:
- Existing FastAPI endpoints
- Current Streamlit interface
- GraphRAG agent architecture
- Caching mechanisms

### 4. Scalability

Architecture supports:
- Horizontal scaling
- Load balancing
- Distributed caching
- Connection pooling

### 5. Observability

Built-in monitoring and logging:
- Performance metrics
- Error tracking
- Usage analytics
- Health checks

## Data Flow

### 1. Tool Invocation

```
Client Request → MCP Server → Tool Manager → GraphRAG Backend → Response
```

### 2. Streaming Updates

```
GraphRAG Backend → Tool Manager → Streaming Manager → SSE → Client
```

### 3. Agent Coordination

```
Query → Agent Coordinator → Multiple Agents → Result Aggregation → Stream Updates
```

### 4. Cache Operations

```
Request → Cache Check → [Hit: Return] / [Miss: Process → Cache → Return]
```

## Technology Stack

### Core Technologies
- **FastMCP**: MCP protocol implementation
- **FastAPI**: HTTP server and API framework
- **asyncio**: Asynchronous programming
- **Server-Sent Events**: Real-time streaming
- **WebSockets**: Bidirectional communication (future)

### Dependencies
- **httpx**: HTTP client library
- **psutil**: System monitoring
- **pydantic**: Data validation
- **uvicorn**: ASGI server

### Integration Points
- **GraphRAG Agents**: Multi-agent coordination
- **Search Engine**: Vector and graph search
- **Cache System**: Distributed caching
- **Knowledge Graph**: Neo4j integration

## Security Considerations

### Authentication
- Optional API key authentication
- Bearer token support
- Client identification

### Authorization
- Tool-level access control
- Rate limiting per client
- Resource usage monitoring

### Data Protection
- Input validation and sanitization
- Error message sanitization
- Secure logging practices

## Performance Characteristics

### Latency
- Tool calls: < 100ms (typical)
- Streaming setup: < 50ms
- Cache operations: < 10ms

### Throughput
- Concurrent connections: 100+ (configurable)
- Requests per second: 1000+ (depends on tool complexity)
- Streaming events: 10,000+ per second

### Resource Usage
- Memory: ~50MB base + tool-specific
- CPU: Low overhead, scales with tool complexity
- Network: Efficient streaming with compression

## Deployment Options

### Development
- Single process with auto-reload
- Debug logging enabled
- Local file-based caching

### Production
- Multi-process deployment
- Load balancer integration
- Distributed caching (Redis)
- Centralized logging

### Container Deployment
- Docker support
- Kubernetes manifests
- Health check endpoints
- Graceful shutdown

## Monitoring and Observability

### Metrics
- Request/response times
- Error rates
- Cache hit rates
- Active connections
- Resource usage

### Logging
- Structured logging (JSON)
- Request/response logging
- Error tracking
- Performance logging

### Health Checks
- Server health endpoint
- Tool availability checks
- Dependency health monitoring
- Performance threshold alerts

## Future Enhancements

### Planned Features
- WebSocket support for bidirectional communication
- Advanced authentication (OAuth2, JWT)
- Tool composition and chaining
- Distributed tool execution
- Enhanced monitoring dashboard

### Scalability Improvements
- Horizontal scaling support
- Load balancing strategies
- Caching optimizations
- Connection pooling

### Integration Enhancements
- Additional protocol support
- Enhanced error recovery
- Advanced streaming patterns
- Tool marketplace

This architecture provides a solid foundation for real-time communication in the GraphRAG system while maintaining flexibility for future enhancements and integrations.
