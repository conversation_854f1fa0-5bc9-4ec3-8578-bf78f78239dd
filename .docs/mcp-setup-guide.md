# GraphRAG MCP Integration Setup Guide

## 🚀 Setting Up MCP in Cursor

This guide shows you how to set up and run the MCP (Model Context Protocol) integration for GraphRAG + DeepSearch in Cursor IDE.

## Prerequisites

- Python 3.8+
- Cursor IDE
- GraphRAG + DeepSearch project

## Quick Start

### 1. Install Dependencies

First, update your `requirements.txt` to include MCP dependencies:

```txt
# MCP Integration Dependencies
fastmcp>=2.9.0
httpx>=0.28.0
websockets>=11.0.0
sse-starlette>=2.3.0
psutil>=5.9.0
```

Then install:
```bash
pip install -r requirements.txt
```

### 2. Run Setup Script

```bash
python setup_mcp_cursor.py
```

This will:
- Install all MCP dependencies
- Create Cursor-specific configuration
- Set up launch scripts and tasks

### 3. Start the MCP Server

**Option A: Using Cursor Tasks (Recommended)**
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Tasks: Run Task"
3. Select "Start MCP Server"

**Option B: Direct command**
```bash
python run_mcp_server.py
```

**Option C: Using launch script**
```bash
./start_mcp.sh    # On macOS/Linux
start_mcp.bat     # On Windows
```

### 4. Test the Integration

In a new terminal tab in Cursor:

```bash
python test_mcp_client.py
```

### 5. Access the MCP Server

Once running, you can access:

- **Server**: http://localhost:8001
- **API Documentation**: http://localhost:8001/docs
- **MCP Endpoint**: http://localhost:8001/mcp
- **Health Check**: http://localhost:8001/health

## Available MCP Tools

### Agent Coordination
- `coordinate_agents` - Multi-agent coordination with streaming
- `get_agent_status` - Check agent status
- `assign_agent_role` - Assign roles to agents
- `broadcast_agent_state` - Real-time agent state updates

### Search Tools
- `local_search` - Local vector search
- `global_search` - Global community search  
- `hybrid_search` - Combined search approach
- `stream_search_results` - Real-time search streaming
- `track_search_progress` - Search progress tracking

### Cache Management
- `get_cache_entry` - Retrieve cached data
- `set_cache_entry` - Store data in cache
- `invalidate_cache` - Clear cache entries
- `get_cache_stats` - Cache performance statistics
- `listen_cache_invalidations` - Real-time cache events

### Knowledge Graph
- `explore_knowledge_graph` - Interactive graph exploration
- `expand_node` - Expand specific nodes
- `find_graph_paths` - Find paths between nodes
- `get_node_details` - Detailed node information
- `search_graph_nodes` - Search for nodes

### Performance Monitoring
- `stream_performance_metrics` - Real-time metrics
- `get_system_metrics` - System performance data
- `get_application_metrics` - Application-specific metrics
- `get_performance_alerts` - Performance alerts
- `generate_performance_report` - Comprehensive reports

## Integration Examples

### Basic Client Usage

```python
from mcp_integration.client import mcp_client_context

async def example_usage():
    async with mcp_client_context("http://localhost:8001") as client:
        # Simple search
        result = await client.search_local("artificial intelligence")
        
        # Streaming search
        async for item in client.stream_search_results("machine learning"):
            print(f"Result: {item}")
        
        # Agent coordination
        async for update in client.coordinate_agents("How does AI work?", ["agent1", "agent2"]):
            print(f"Agent update: {update}")
```

### Server Integration

```python
from mcp_integration.server import create_graphrag_mcp_server
from mcp_integration.config import MCPConfig
from fastapi import FastAPI

app = FastAPI()

# Create MCP configuration
config = MCPConfig(
    server_name="My-GraphRAG-MCP",
    enable_streaming=True,
    enable_agent_coordination=True
)

# Integrate MCP with existing FastAPI app
mcp_server = create_graphrag_mcp_server(config, app)
```

## Configuration

### Environment Variables

```bash
# Server Configuration
MCP_SERVER_NAME="GraphRAG-DeepSearch-MCP"
MCP_HOST="localhost"
MCP_PORT="8001"

# Features
MCP_ENABLE_STREAMING="true"
MCP_ENABLE_AGENT_COORDINATION="true"
MCP_ENABLE_DISTRIBUTED_CACHE="true"
MCP_ENABLE_PERFORMANCE_MONITORING="true"

# Logging
MCP_LOG_LEVEL="INFO"
MCP_LOG_REQUESTS="true"
```

### Cursor Tasks

The setup creates these Cursor tasks:

- **Start MCP Server** - Launch the MCP server
- **Test MCP Client** - Run client tests
- **Run MCP Tests** - Execute the test suite

Access via `Ctrl+Shift+P` → "Tasks: Run Task"

## Troubleshooting

### Common Issues

1. **Port 8001 is busy**
   - Change port in `run_mcp_server.py`
   - Or set `MCP_PORT` environment variable

2. **Dependencies not found**
   - Run: `pip install fastmcp httpx websockets sse-starlette psutil`
   - Check: `pip list | grep fastmcp`

3. **Connection refused**
   - Ensure MCP server is running
   - Check firewall settings
   - Verify port is not blocked

4. **Import errors**
   - Ensure project root is in Python path
   - Check virtual environment activation

### Debug Mode

Enable debug logging:
```bash
export MCP_LOG_LEVEL="DEBUG"
python run_mcp_server.py
```

### Log Files

- Server logs: `mcp_server.log`
- Check Cursor's integrated terminal for real-time logs

## Development Tips

1. **Keep the MCP server running** while developing
2. **Use Cursor's integrated terminal** for real-time logs
3. **Check API docs** at http://localhost:8001/docs
4. **Use test client** to verify functionality
5. **Restart server** after code changes

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Cursor IDE    │    │   MCP Server    │    │   GraphRAG      │
│                 │    │                 │    │   Backend       │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   Client    │◄┼────┼►│   FastMCP   │◄┼────┼►│   Agents    │ │
│ │   Scripts   │ │    │ │   Server    │ │    │ │   Search    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ │   Cache     │ │
│                 │    │                 │    │ │   Graph     │ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ └─────────────┘ │
│ │   Terminal  │ │    │ │  Streaming  │ │    │                 │
│ │   Tasks     │ │    │ │  Manager    │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Next Steps

1. **Start the MCP server** using Cursor tasks
2. **Test basic functionality** with the client script
3. **Integrate with your GraphRAG code** using the examples
4. **Explore streaming features** for real-time updates
5. **Monitor performance** using the built-in tools

For more detailed information, check the API documentation at http://localhost:8001/docs when the server is running.
