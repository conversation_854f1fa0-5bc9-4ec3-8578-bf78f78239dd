# 🚀 MCP Transport Deployment Guide

## 📋 **Overview**

This guide provides complete deployment instructions for all three MCP transport protocols:

1. **SSE Transport** - Legacy web-based transport
2. **Streamable HTTP Transport** - Modern web-based transport (recommended)
3. **STDIO Transport** - Local process transport for Claude Desktop

---

## 🌐 **Web-Based Deployment (SSE + Streamable HTTP)**

### Production Deployment

#### 1. Using Uvicorn (Recommended)
```bash
# Install dependencies
pip install uvicorn fastapi fastmcp

# Start server
cd /path/to/graph-rag-agent
uvicorn run_mcp_server:create_mcp_app --host 0.0.0.0 --port 8001 --factory

# With SSL (Production)
uvicorn run_mcp_server:create_mcp_app --host 0.0.0.0 --port 8001 --factory \
  --ssl-keyfile /path/to/private.key \
  --ssl-certfile /path/to/certificate.crt
```

#### 2. Using Gun<PERSON> + Uvicorn Workers
```bash
# Install gunicorn
pip install gunicorn

# Start with multiple workers
gunicorn run_mcp_server:create_mcp_app \
  --worker-class uvicorn.workers.UvicornWorker \
  --workers 4 \
  --bind 0.0.0.0:8001 \
  --factory
```

#### 3. Docker Deployment
```dockerfile
# Dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8001
CMD ["uvicorn", "run_mcp_server:create_mcp_app", "--host", "0.0.0.0", "--port", "8001", "--factory"]
```

```bash
# Build and run
docker build -t graphrag-mcp .
docker run -p 8001:8001 graphrag-mcp
```

#### 4. Systemd Service (Linux)
```ini
# /etc/systemd/system/graphrag-mcp.service
[Unit]
Description=GraphRAG MCP Server
After=network.target

[Service]
Type=exec
User=mcp
Group=mcp
WorkingDirectory=/opt/graph-rag-agent
Environment=PATH=/opt/graph-rag-agent/.venv/bin
ExecStart=/opt/graph-rag-agent/.venv/bin/uvicorn run_mcp_server:create_mcp_app --host 0.0.0.0 --port 8001 --factory
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable graphrag-mcp
sudo systemctl start graphrag-mcp
sudo systemctl status graphrag-mcp
```

### Development Deployment

#### Local Development
```bash
# Quick start for development
cd /path/to/graph-rag-agent
python run_mcp_server.py

# Or with auto-reload
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8001 --factory --reload
```

#### Development with Hot Reload
```bash
# Install development dependencies
pip install watchdog

# Start with file watching
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8001 --factory --reload --reload-dir ./mcp_integration
```

---

## 🖥️ **Claude Desktop Integration (STDIO)**

### 1. Basic Claude Desktop Configuration

#### macOS/Linux Configuration
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python",
      "args": ["/absolute/path/to/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/absolute/path/to/graph-rag-agent"
      }
    }
  }
}
```

#### Windows Configuration
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python.exe",
      "args": ["C:\\path\\to\\graph-rag-agent\\run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "C:\\path\\to\\graph-rag-agent"
      }
    }
  }
}
```

### 2. Virtual Environment Configuration

#### Using Virtual Environment
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "/path/to/graph-rag-agent/.venv/bin/python",
      "args": ["/path/to/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/path/to/graph-rag-agent"
      }
    }
  }
}
```

#### Using Conda Environment
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "/path/to/conda/envs/graphrag/bin/python",
      "args": ["/path/to/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/path/to/graph-rag-agent",
        "CONDA_DEFAULT_ENV": "graphrag"
      }
    }
  }
}
```

### 3. Advanced STDIO Configuration

#### With Custom Configuration
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python",
      "args": [
        "/path/to/graph-rag-agent/run_mcp_stdio.py",
        "--config", "/path/to/custom-config.json"
      ],
      "env": {
        "PYTHONPATH": "/path/to/graph-rag-agent",
        "MCP_LOG_LEVEL": "DEBUG"
      }
    }
  }
}
```

#### With Resource Limits
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python",
      "args": ["/path/to/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/path/to/graph-rag-agent"
      },
      "timeout": 30000,
      "memoryLimit": "512MB"
    }
  }
}
```

---

## 🔧 **Environment Configuration**

### Required Environment Variables

#### For Web Deployment
```bash
# .env file
MCP_HOST=0.0.0.0
MCP_PORT=8001
MCP_LOG_LEVEL=INFO
MCP_ENABLE_STREAMING=true
MCP_ENABLE_AGENT_COORDINATION=true
MCP_ENABLE_DISTRIBUTED_CACHE=true
MCP_ENABLE_PERFORMANCE_MONITORING=true
```

#### For STDIO Deployment
```bash
# .env file for STDIO
MCP_LOG_LEVEL=INFO
MCP_ENABLE_STREAMING=true
MCP_ENABLE_AGENT_COORDINATION=true
MCP_ENABLE_DISTRIBUTED_CACHE=true
MCP_ENABLE_PERFORMANCE_MONITORING=true
PYTHONPATH=/path/to/graph-rag-agent
```

### Dependencies Installation

#### Production Dependencies
```bash
pip install fastapi uvicorn fastmcp asyncio logging pathlib
```

#### Development Dependencies
```bash
pip install fastapi uvicorn fastmcp asyncio logging pathlib watchdog pytest
```

---

## 🌍 **Network Configuration**

### Firewall Rules

#### Linux (iptables)
```bash
# Allow MCP server port
sudo iptables -A INPUT -p tcp --dport 8001 -j ACCEPT
sudo iptables-save > /etc/iptables/rules.v4
```

#### Linux (ufw)
```bash
# Allow MCP server port
sudo ufw allow 8001/tcp
sudo ufw reload
```

### Reverse Proxy Configuration

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/graphrag-mcp
server {
    listen 80;
    server_name your-domain.com;

    location /mcp/ {
        proxy_pass http://127.0.0.1:8001/mcp/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # For SSE support
        proxy_buffering off;
        proxy_cache off;
    }
}
```

#### Apache Configuration
```apache
# /etc/apache2/sites-available/graphrag-mcp.conf
<VirtualHost *:80>
    ServerName your-domain.com
    
    ProxyPreserveHost On
    ProxyRequests Off
    
    # For MCP endpoints
    ProxyPass /mcp/ http://127.0.0.1:8001/mcp/
    ProxyPassReverse /mcp/ http://127.0.0.1:8001/mcp/
    
    # For SSE support
    ProxyPass /mcp/sse/ http://127.0.0.1:8001/mcp/sse/
    ProxyPassReverse /mcp/sse/ http://127.0.0.1:8001/mcp/sse/
</VirtualHost>
```

---

## 📊 **Monitoring & Logging**

### Log Configuration

#### Structured Logging
```python
# logging_config.py
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        return json.dumps(log_entry)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/graphrag-mcp.log'),
        logging.StreamHandler()
    ]
)
```

### Health Monitoring

#### Health Check Endpoint
```bash
# Check server health
curl http://localhost:8001/health

# Expected response
{"status": "healthy", "service": "GraphRAG-MCP-Server"}
```

#### Transport Validation
```bash
# Test all transports
python test_all_three_transports.py

# Test specific transport
curl -v http://localhost:8001/mcp/sse/
curl -v http://localhost:8001/mcp/http/
```

---

## 🚨 **Troubleshooting**

### Common Issues

#### Port Already in Use
```bash
# Find process using port 8001
lsof -ti:8001

# Kill process
lsof -ti:8001 | xargs kill -9

# Or use different port
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8002 --factory
```

#### Permission Denied (Linux)
```bash
# Allow non-root user to bind to port 8001
sudo setcap 'cap_net_bind_service=+ep' /usr/bin/python3

# Or run as root (not recommended for production)
sudo uvicorn run_mcp_server:create_mcp_app --host 0.0.0.0 --port 8001 --factory
```

#### STDIO Not Working in Claude Desktop
```bash
# Test STDIO manually
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0"}}}' | python run_mcp_stdio.py

# Check Claude Desktop logs
tail -f ~/Library/Logs/Claude/mcp-server-*.log
```

### Debug Commands

#### Server Status
```bash
# Check if server is running
curl -I http://localhost:8001/health

# Check transport endpoints
curl -v http://localhost:8001/mcp/sse/
curl -v http://localhost:8001/mcp/http/
```

#### Process Monitoring
```bash
# Monitor server process
ps aux | grep python | grep mcp

# Monitor network connections
netstat -tlnp | grep 8001

# Monitor logs in real-time
tail -f /var/log/graphrag-mcp.log
```

---

## ✅ **Deployment Checklist**

### Pre-Deployment
- [ ] Dependencies installed
- [ ] Environment variables configured
- [ ] Firewall rules configured
- [ ] SSL certificates ready (production)
- [ ] Monitoring setup

### Web Deployment
- [ ] Server starts without errors
- [ ] Both SSE and HTTP transports accessible
- [ ] All 33 tools responding
- [ ] Health check endpoint working
- [ ] Logs being written correctly

### STDIO Deployment
- [ ] Claude Desktop configuration updated
- [ ] STDIO server responds to initialize
- [ ] All tools accessible via STDIO
- [ ] Error logging to stderr working

### Post-Deployment
- [ ] Transport validation tests pass
- [ ] Performance monitoring active
- [ ] Backup procedures in place
- [ ] Documentation updated

---

**🎯 This guide covers all deployment scenarios for your MCP transport protocols!**
