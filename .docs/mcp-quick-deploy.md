# 🚀 MCP Quick Deploy Reference

## ⚡ **Quick Start Commands**

### 1. Web Server (SSE + Streamable HTTP)
```bash
# Development
cd /path/to/graph-rag-agent
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8001 --factory

# Production
uvicorn run_mcp_server:create_mcp_app --host 0.0.0.0 --port 8001 --factory
```

### 2. STDIO Server (<PERSON>)
```bash
# Test STDIO
cd /path/to/graph-rag-agent
python run_mcp_stdio.py

# Claude Desktop config location:
# macOS: ~/Library/Application Support/Claude/claude_desktop_config.json
# Windows: %APPDATA%/Claude/claude_desktop_config.json
```

### 3. Validate All Transports
```bash
cd /path/to/graph-rag-agent
python test_all_three_transports.py
```

---

## 📋 **Claude Desktop Configuration**

### Basic Configuration
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python",
      "args": ["/ABSOLUTE/PATH/TO/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/ABSOLUTE/PATH/TO/graph-rag-agent"
      }
    }
  }
}
```

### With Virtual Environment
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "/ABSOLUTE/PATH/TO/graph-rag-agent/.venv/bin/python",
      "args": ["/ABSOLUTE/PATH/TO/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/ABSOLUTE/PATH/TO/graph-rag-agent"
      }
    }
  }
}
```

---

## 🔗 **Transport Endpoints**

| Transport | URL | Use Case |
|-----------|-----|----------|
| **SSE** | `http://localhost:8001/mcp/sse` | Legacy web clients |
| **Streamable HTTP** | `http://localhost:8001/mcp/http` | Modern web clients |
| **STDIO** | `python run_mcp_stdio.py` | Claude Desktop |

---

## 🧪 **Quick Tests**

### Test Web Transports
```bash
# SSE
curl -v http://localhost:8001/mcp/sse/

# Streamable HTTP
curl -v http://localhost:8001/mcp/http/

# Health Check
curl http://localhost:8001/health
```

### Test STDIO
```bash
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0"}}}' | python run_mcp_stdio.py
```

---

## 💡 **Usage Examples**

### Streamable HTTP Protocol Testing

#### Basic Connection Test
```bash
# Test if Streamable HTTP endpoint is accessible
curl -v http://localhost:8001/mcp/http/

# Expected: 406 Not Acceptable with mcp-session-id header
# This confirms the transport is working
```

#### Initialize MCP Session (Basic Test)
```bash
# This will initialize a session and return session info
curl -X POST http://localhost:8001/mcp/http/ \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {"name": "curl-client", "version": "1.0"}
    }
  }'

# Expected response: JSON-RPC result with server info
```

#### ⚠️ **Important Note about Direct curl Usage**

**Streamable HTTP transport is optimized for programmatic clients** (like FastMCP Python client) rather than direct curl usage. While basic connection and initialization work with curl, **tool listing and calling require proper MCP client libraries** for full functionality.

**✅ Recommended: Use FastMCP Python Client**
```python
# This is the proper way to use Streamable HTTP
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def use_mcp():
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        # List tools
        tools = await client.list_tools()

        # Call tools
        result = await client.call_tool("local_search", {
            "query": "artificial intelligence",
            "max_results": 5
        })
```

#### curl Alternative: Use SSE Transport
```bash
# For direct curl usage, SSE transport is more suitable
curl -v http://localhost:8001/mcp/sse/
# This provides an endpoint URL for further MCP communication
```

### Python Client Examples

#### FastMCP Client (Recommended)
```python
import asyncio
import json
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def use_streamable_http():
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        # List tools
        tools = await client.list_tools()
        print(f"Available tools: {len(tools)}")

        # Call local search
        result = await client.call_tool("local_search", {
            "query": "machine learning",
            "max_results": 3
        })

        # Parse result
        content = json.loads(result[0].text)
        print(f"Search results: {content}")

# Run the example
asyncio.run(use_streamable_http())
```

#### SSE Transport Example
```python
import asyncio
from fastmcp import Client
from fastmcp.client.transports import SSETransport

async def use_sse():
    transport = SSETransport(url="http://localhost:8001/mcp/sse")
    client = Client(transport)

    async with client:
        # Call cache operations
        result = await client.call_tool("set_cache_entry", {
            "cache_key": "test_key",
            "value": {"data": "test_value"},
            "cache_type": "user_cache"
        })

        print(f"Cache set result: {result}")

asyncio.run(use_sse())
```

### JavaScript/Node.js Examples

#### ⚠️ **Recommended: Use MCP Client Library**
```javascript
// For JavaScript/Node.js, use a proper MCP client library
// Direct fetch API usage with Streamable HTTP is complex

// Example with hypothetical MCP client library:
import { MCPClient } from '@modelcontextprotocol/client';

const client = new MCPClient({
  transport: 'streamable-http',
  url: 'http://localhost:8001/mcp/http'
});

await client.connect();
const tools = await client.listTools();
const result = await client.callTool('local_search', {
  query: 'artificial intelligence',
  max_results: 5
});
```

#### Basic Connection Test with fetch
```javascript
// Test if the endpoint is accessible
const testResponse = await fetch('http://localhost:8001/mcp/http/', {
  method: 'GET'
});

console.log('Status:', testResponse.status); // Should be 406
console.log('Session ID:', testResponse.headers.get('mcp-session-id'));

// Initialize session (basic test)
const initResponse = await fetch('http://localhost:8001/mcp/http/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json, text/event-stream'
  },
  body: JSON.stringify({
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: { name: "js-client", version: "1.0" }
    }
  })
});

const initResult = await initResponse.text();
console.log('Initialize result:', initResult);
```

### Working Tool Examples (Python)

#### Knowledge Graph Exploration
```python
import asyncio
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def explore_knowledge_graph():
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        result = await client.call_tool("explore_knowledge_graph", {
            "query": "neural networks",
            "max_depth": 3,
            "include_relationships": True
        })
        print("Knowledge graph result:", result)

asyncio.run(explore_knowledge_graph())
```

#### Performance Monitoring
```python
async def get_performance_metrics():
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        result = await client.call_tool("get_application_metrics", {})
        print("Performance metrics:", result)

asyncio.run(get_performance_metrics())
```

#### Cache Operations
```python
async def cache_operations():
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        # Set cache entry
        set_result = await client.call_tool("set_cache_entry", {
            "cache_key": "user_preferences",
            "value": {"theme": "dark", "language": "en"},
            "cache_type": "user_cache"
        })
        print("Cache set result:", set_result)

        # Get cache entry
        get_result = await client.call_tool("get_cache_entry", {
            "cache_key": "user_preferences",
            "cache_type": "user_cache"
        })
        print("Cache get result:", get_result)

asyncio.run(cache_operations())
```

#### Agent Coordination
```python
async def agent_coordination():
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        # Get agent status
        status = await client.call_tool("get_agent_status", {
            "agent_id": "naive_rag_agent"
        })
        print("Agent status:", status)

        # Coordinate agents
        coordination = await client.call_tool("coordinate_agents", {
            "query": "complex research task",
            "agent_types": ["search", "analysis", "synthesis"],
            "coordination_strategy": "parallel"
        })
        print("Agent coordination:", coordination)

asyncio.run(agent_coordination())
```

### Complete Working Example (Python)

Here's a complete working example that demonstrates all MCP functionality:

```python
#!/usr/bin/env python3
"""
Complete MCP Streamable HTTP Example
Demonstrates all major MCP operations with proper error handling
"""

import asyncio
import json
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def complete_mcp_example():
    """Complete example showing all MCP operations"""

    print("🚀 Testing MCP Streamable HTTP Protocol")

    # Initialize transport and client
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    try:
        async with client:
            print("📡 Step 1: Connected to MCP server")

            # Step 2: List available tools
            print("📋 Step 2: Listing available tools...")
            tools = await client.list_tools()
            print(f"Found {len(tools)} tools:")
            for i, tool in enumerate(tools[:5]):  # Show first 5 tools
                print(f"  {i+1}. {tool.name}: {tool.description[:60]}...")

            # Step 3: Call local search tool
            print("\n🔍 Step 3: Calling local_search tool...")
            search_result = await client.call_tool("local_search", {
                "query": "machine learning",
                "max_results": 3
            })

            if search_result:
                content = json.loads(search_result[0].text)
                print(f"Search found {len(content.get('results', []))} results")
                for result in content.get('results', [])[:2]:
                    print(f"  - {result.get('title', 'No title')}")

            # Step 4: Get agent status
            print("\n👥 Step 4: Checking agent status...")
            agent_result = await client.call_tool("get_agent_status", {
                "agent_id": "test_agent"
            })

            if agent_result:
                agent_content = json.loads(agent_result[0].text)
                print(f"Agent status: {agent_content.get('status', 'unknown')}")

            # Step 5: Cache operations
            print("\n💾 Step 5: Testing cache operations...")

            # Set cache entry
            set_result = await client.call_tool("set_cache_entry", {
                "cache_key": "test_key",
                "value": {"timestamp": "2025-06-29", "test": True},
                "cache_type": "test_cache"
            })

            if set_result:
                set_content = json.loads(set_result[0].text)
                print(f"Cache set: {set_content.get('success', False)}")

            # Get cache entry
            get_result = await client.call_tool("get_cache_entry", {
                "cache_key": "test_key",
                "cache_type": "test_cache"
            })

            if get_result:
                get_content = json.loads(get_result[0].text)
                print(f"Cache retrieved: {get_content.get('found', False)}")

            print("\n✅ MCP Streamable HTTP test complete!")

    except Exception as e:
        print(f"❌ Error: {e}")

# Run the example
if __name__ == "__main__":
    asyncio.run(complete_mcp_example())
```

### Basic Connection Test (bash)

For simple connectivity testing, you can use this bash script:

```bash
#!/bin/bash
# Basic MCP connection test

echo "🧪 Testing MCP endpoint connectivity..."

# Test basic endpoint
echo "📡 Testing basic endpoint..."
curl -s -v http://localhost:8001/mcp/http/ 2>&1 | grep -E "(HTTP|mcp-session-id)"

# Test initialize (basic)
echo -e "\n📋 Testing initialize..."
curl -s -X POST http://localhost:8001/mcp/http/ \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {"name": "test-client", "version": "1.0"}
    }
  }' | grep -o '"result":{[^}]*}'

echo -e "\n✅ Basic connectivity test complete!"
echo "💡 For full functionality, use the Python example above"
```

---

## 🔌 **FastAPI Endpoints vs MCP Protocol**

### Current Architecture

**❌ No Direct REST API for MCP Tools**
- The MCP server exposes **MCP protocol endpoints** only (`/mcp/sse`, `/mcp/http`)
- MCP tools are **not accessible via REST API** (e.g., no `/api/tools/local_search`)
- This is by design - MCP is a **protocol**, not a REST API

### Available FastAPI Endpoints

```bash
# Health check endpoints
curl http://localhost:8001/health
curl http://localhost:8001/mcp/health  # (may have issues)

# Root endpoint
curl http://localhost:8001/

# OpenAPI docs
curl http://localhost:8001/docs
curl http://localhost:8001/openapi.json
```

### Why No REST API?

1. **MCP is a Protocol** - Model Context Protocol is designed for AI model integration, not REST APIs
2. **Session Management** - MCP requires persistent sessions and state management
3. **Streaming Support** - MCP supports real-time streaming, which REST APIs don't handle well
4. **Type Safety** - MCP provides strong typing and validation that REST APIs would lose

### If You Need REST API Access

If you need REST API access to MCP tools, you would need to create a **wrapper layer**:

```python
# Example REST API wrapper (not implemented)
from fastapi import APIRouter
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

router = APIRouter(prefix="/api/v1")

@router.post("/tools/search")
async def rest_search(query: str, max_results: int = 5):
    """REST wrapper for MCP local_search tool"""
    transport = StreamableHttpTransport(url="http://localhost:8001/mcp/http")
    client = Client(transport)

    async with client:
        result = await client.call_tool("local_search", {
            "query": query,
            "max_results": max_results
        })
        return {"result": result}

# This would enable: curl -X POST /api/v1/tools/search -d '{"query": "test"}'
```

### Recommendation

**✅ Use MCP Protocol Directly**
- More efficient and feature-complete
- Proper session management
- Real-time streaming support
- Type-safe tool calling

**🔧 For Integration:**
- Use FastMCP Python client
- Create your own REST wrapper if needed
- Use the provided Python examples
```

---

## 🐛 **Quick Troubleshooting**

### Port Issues
```bash
# Kill process on port 8001
lsof -ti:8001 | xargs kill -9

# Use different port
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8002 --factory
```

### STDIO Issues
```bash
# Check Python path
which python

# Test STDIO manually
python run_mcp_stdio.py < test_input.json

# Check Claude Desktop logs (macOS)
tail -f ~/Library/Logs/Claude/mcp-server-*.log
```

### Dependencies
```bash
# Install missing dependencies
pip install fastapi uvicorn fastmcp

# Check installation
python -c "import fastmcp; print('FastMCP installed')"
```

---

## 📁 **File Locations**

```
graph-rag-agent/
├── run_mcp_server.py          # Web server (SSE + HTTP)
├── run_mcp_stdio.py           # STDIO server (Claude Desktop)
├── test_all_three_transports.py  # Complete validation
├── .docs/
│   ├── mcp-deployment-guide.md    # Full deployment guide
│   ├── mcp-transport-implementation.md  # Technical details
│   └── mcp-quick-deploy.md        # This quick reference
└── mcp_integration/
    ├── server.py              # Core MCP server
    ├── config.py              # Configuration
    └── tools/                 # 33 MCP tools
```

---

## ✅ **Success Indicators**

### Web Server Running
- ✅ Server starts without errors
- ✅ `curl http://localhost:8001/health` returns `{"status": "healthy"}`
- ✅ Both `/mcp/sse` and `/mcp/http` respond
- ✅ Test script shows "3/3 transports successful"

### Claude Desktop Integration
- ✅ STDIO test returns JSON-RPC response
- ✅ Claude Desktop shows GraphRAG tools available
- ✅ No errors in Claude Desktop logs
- ✅ Tools execute successfully in Claude

---

## 🎯 **Production Checklist**

- [ ] Change host to `0.0.0.0` for external access
- [ ] Configure SSL certificates
- [ ] Set up reverse proxy (nginx/apache)
- [ ] Configure firewall rules
- [ ] Set up monitoring and logging
- [ ] Test all transport protocols
- [ ] Backup configuration files

---

**🚀 Your MCP server with 3 transport protocols is ready to deploy!**
