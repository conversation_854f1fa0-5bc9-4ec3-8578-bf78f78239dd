# 🚀 MCP Quick Deploy Reference

## ⚡ **Quick Start Commands**

### 1. Web Server (SSE + Streamable HTTP)
```bash
# Development
cd /path/to/graph-rag-agent
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8001 --factory

# Production
uvicorn run_mcp_server:create_mcp_app --host 0.0.0.0 --port 8001 --factory
```

### 2. STDIO Server (<PERSON>)
```bash
# Test STDIO
cd /path/to/graph-rag-agent
python run_mcp_stdio.py

# Claude Desktop config location:
# macOS: ~/Library/Application Support/Claude/claude_desktop_config.json
# Windows: %APPDATA%/Claude/claude_desktop_config.json
```

### 3. Validate All Transports
```bash
cd /path/to/graph-rag-agent
python test_all_three_transports.py
```

---

## 📋 **Claude Desktop Configuration**

### Basic Configuration
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "python",
      "args": ["/ABSOLUTE/PATH/TO/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/ABSOLUTE/PATH/TO/graph-rag-agent"
      }
    }
  }
}
```

### With Virtual Environment
```json
{
  "mcpServers": {
    "graphrag-deepsearch": {
      "command": "/ABSOLUTE/PATH/TO/graph-rag-agent/.venv/bin/python",
      "args": ["/ABSOLUTE/PATH/TO/graph-rag-agent/run_mcp_stdio.py"],
      "env": {
        "PYTHONPATH": "/ABSOLUTE/PATH/TO/graph-rag-agent"
      }
    }
  }
}
```

---

## 🔗 **Transport Endpoints**

| Transport | URL | Use Case |
|-----------|-----|----------|
| **SSE** | `http://localhost:8001/mcp/sse` | Legacy web clients |
| **Streamable HTTP** | `http://localhost:8001/mcp/http` | Modern web clients |
| **STDIO** | `python run_mcp_stdio.py` | Claude Desktop |

---

## 🧪 **Quick Tests**

### Test Web Transports
```bash
# SSE
curl -v http://localhost:8001/mcp/sse/

# Streamable HTTP  
curl -v http://localhost:8001/mcp/http/

# Health Check
curl http://localhost:8001/health
```

### Test STDIO
```bash
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0"}}}' | python run_mcp_stdio.py
```

---

## 🐛 **Quick Troubleshooting**

### Port Issues
```bash
# Kill process on port 8001
lsof -ti:8001 | xargs kill -9

# Use different port
uvicorn run_mcp_server:create_mcp_app --host localhost --port 8002 --factory
```

### STDIO Issues
```bash
# Check Python path
which python

# Test STDIO manually
python run_mcp_stdio.py < test_input.json

# Check Claude Desktop logs (macOS)
tail -f ~/Library/Logs/Claude/mcp-server-*.log
```

### Dependencies
```bash
# Install missing dependencies
pip install fastapi uvicorn fastmcp

# Check installation
python -c "import fastmcp; print('FastMCP installed')"
```

---

## 📁 **File Locations**

```
graph-rag-agent/
├── run_mcp_server.py          # Web server (SSE + HTTP)
├── run_mcp_stdio.py           # STDIO server (Claude Desktop)
├── test_all_three_transports.py  # Complete validation
├── .docs/
│   ├── mcp-deployment-guide.md    # Full deployment guide
│   ├── mcp-transport-implementation.md  # Technical details
│   └── mcp-quick-deploy.md        # This quick reference
└── mcp_integration/
    ├── server.py              # Core MCP server
    ├── config.py              # Configuration
    └── tools/                 # 33 MCP tools
```

---

## ✅ **Success Indicators**

### Web Server Running
- ✅ Server starts without errors
- ✅ `curl http://localhost:8001/health` returns `{"status": "healthy"}`
- ✅ Both `/mcp/sse` and `/mcp/http` respond
- ✅ Test script shows "3/3 transports successful"

### Claude Desktop Integration
- ✅ STDIO test returns JSON-RPC response
- ✅ Claude Desktop shows GraphRAG tools available
- ✅ No errors in Claude Desktop logs
- ✅ Tools execute successfully in Claude

---

## 🎯 **Production Checklist**

- [ ] Change host to `0.0.0.0` for external access
- [ ] Configure SSL certificates
- [ ] Set up reverse proxy (nginx/apache)
- [ ] Configure firewall rules
- [ ] Set up monitoring and logging
- [ ] Test all transport protocols
- [ ] Backup configuration files

---

**🚀 Your MCP server with 3 transport protocols is ready to deploy!**
