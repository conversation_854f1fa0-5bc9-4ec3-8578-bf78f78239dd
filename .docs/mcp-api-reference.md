# GraphRAG MCP API Reference

## Overview

The GraphRAG MCP (Model Context Protocol) integration provides real-time communication capabilities for the GraphRAG + DeepSearch system. This document describes all available tools and their usage.

## Base URL

**IMPORTANT**: This MCP server uses the **MCP Protocol**, NOT REST endpoints.

**MCP SSE Endpoint**: `http://localhost:8001/mcp/sse`

## Authentication

Currently, authentication is optional. When enabled, use API key in MCP protocol messages.

## Quick Start

### ⚠️ IMPORTANT: Use MCP Protocol, NOT REST

This server uses the **Model Context Protocol (MCP)**, not REST endpoints. You cannot call tools with HTTP POST requests to `/mcp/tools/tool_name`.

### Correct Python Example (FastMCP Client)

```python
import asyncio
from fastmcp import Client
from fastmcp.client.transports import SSETransport

async def call_local_search():
    # Create SSE transport for MCP protocol
    transport = SSETransport(url="http://localhost:8001/mcp/sse")
    client = Client(transport)

    async with client:
        # Call the local_search tool using MCP protocol
        result = await client.call_tool("local_search", {
            "query": "artificial intelligence",
            "max_results": 5
        })

        # Parse the result (FastMCP returns TextContent objects)
        if isinstance(result, list) and len(result) > 0:
            import json
            content = json.loads(result[0].text)
            return content

        return result

# Run the example
result = asyncio.run(call_local_search())
print(f"Found {len(result['results'])} results")
print(f"Search type: {result['search_type']}")
print(f"Execution time: {result['execution_time']:.3f}s")
```

### ❌ INCORRECT: REST Endpoints (These Don't Work)

```bash
# ❌ This will return "Not Found"
curl -X POST "http://localhost:8001/mcp/tools/local_search" \
  -H "Content-Type: application/json" \
  -d '{"query": "test"}'
```

**Why this doesn't work**: Our server uses FastMCP with SSE transport, which implements the MCP protocol, not REST endpoints.

## Tool Categories

### 1. Agent Coordination Tools

#### `coordinate_agents`
Coordinate multiple agents for complex query processing with real-time updates.

**Parameters:**
- `query` (string, required): Query to process
- `agents` (array, required): List of agent IDs
- `strategy` (string, optional): Coordination strategy ("parallel", "sequential", "adaptive")

**Response:** Streaming updates with agent status

**Example:**
```python
async for update in client.coordinate_agents(
    "How does AI impact healthcare?",
    agents=["naive_rag_agent", "graph_agent", "hybrid_agent"],
    strategy="parallel"
):
    print(f"Agent {update['agent_id']}: {update['status']}")
```

#### `broadcast_agent_state`
Stream real-time agent state changes.

**Parameters:**
- `agent_id` (string, required): Agent identifier
- `session_id` (string, optional): Coordination session ID

**Response:** Streaming agent state updates

#### `get_agent_status`
Get current status of a specific agent.

**Parameters:**
- `agent_id` (string, required): Agent identifier

**Response:**
```json
{
  "agent_id": "naive_rag_agent",
  "status": "active",
  "current_state": {...},
  "last_updated": **********.0
}
```

#### `assign_agent_role`
Assign appropriate roles to agents based on query characteristics.

**Parameters:**
- `query` (string, required): Query to analyze
- `query_type` (string, optional): Type of query
- `complexity_level` (string, optional): Complexity level

**Response:**
```json
{
  "query": "How does AI work?",
  "assignments": [
    {
      "agent_id": "naive_rag_agent",
      "role": "primary",
      "priority": 1,
      "reasoning": "Simple factual query suitable for basic RAG"
    }
  ],
  "coordination_strategy": "parallel"
}
```

### 2. Search Tools

#### `local_search`
Execute local search using vector similarity.

**Parameters:**
- `query` (string, required): Search query
- `max_results` (integer, optional): Maximum results (default: 10)
- `community_level` (integer, optional): Community level filter

**Response:**
```json
{
  "query": "artificial intelligence",
  "results": [
    {
      "id": "local_result_0",
      "content": "Local search result content...",
      "score": 0.9,
      "source": "document_0.pdf",
      "community_id": "community_0"
    }
  ],
  "search_type": "local",
  "execution_time": 0.5
}
```

#### `global_search`
Execute global search across knowledge graph communities.

**Parameters:**
- `query` (string, required): Search query
- `community_level` (integer, optional): Community level (default: 2)
- `response_type` (string, optional): Response format

**Response:**
```json
{
  "query": "machine learning",
  "communities": [
    {
      "community_id": "community_0",
      "summary": "Community summary...",
      "relevance_score": 0.8,
      "entities": ["entity_0_0", "entity_0_1"],
      "relationships": ["rel_0_0", "rel_0_1"]
    }
  ],
  "global_answer": "Global analysis...",
  "search_type": "global"
}
```

#### `hybrid_search`
Execute hybrid search combining local and global results.

**Parameters:**
- `query` (string, required): Search query
- `local_weight` (float, optional): Weight for local results (default: 0.6)
- `global_weight` (float, optional): Weight for global results (default: 0.4)
- `max_results` (integer, optional): Maximum results (default: 15)

#### `stream_search_results`
Stream search results as they become available.

**Parameters:**
- `query` (string, required): Search query
- `search_type` (string, optional): Type ("local", "global", "hybrid")
- `max_results` (integer, optional): Maximum results

**Response:** Streaming search results

#### `track_search_progress`
Track and stream search progress updates.

**Parameters:**
- `query` (string, required): Search query

**Response:** Streaming progress updates

### 3. Cache Management Tools

#### `get_cache_entry`
Retrieve entry from cache.

**Parameters:**
- `cache_key` (string, required): Cache key
- `cache_type` (string, optional): Cache type (default: "query_results")

**Response:**
```json
{
  "cache_key": "test_key",
  "cache_type": "query_results",
  "found": true,
  "value": {"data": "cached_value"},
  "metadata": {...},
  "hit_count": 5
}
```

#### `set_cache_entry`
Store entry in cache.

**Parameters:**
- `cache_key` (string, required): Cache key
- `value` (any, required): Value to cache
- `cache_type` (string, optional): Cache type
- `ttl` (integer, optional): Time to live in seconds
- `metadata` (object, optional): Additional metadata

#### `invalidate_cache`
Invalidate cache entries matching pattern.

**Parameters:**
- `cache_pattern` (string, required): Pattern to match ("*" for all)
- `cache_type` (string, optional): Cache type filter

#### `get_cache_stats`
Get cache performance statistics.

**Response:**
```json
{
  "total_entries": 1250,
  "cache_hits": 8500,
  "cache_misses": 1500,
  "hit_rate": 0.85,
  "total_size_bytes": 1048576,
  "cache_types": {...}
}
```

### 4. Knowledge Graph Tools

#### `explore_knowledge_graph`
Stream knowledge graph exploration results.

**Parameters:**
- `start_node` (string, required): Starting node ID
- `max_depth` (integer, optional): Maximum exploration depth (default: 3)
- `max_nodes` (integer, optional): Maximum nodes to explore (default: 50)
- `relationship_types` (array, optional): Filter relationship types

**Response:** Streaming graph elements (nodes and relationships)

#### `expand_node`
Interactively expand a specific node.

**Parameters:**
- `node_id` (string, required): Node to expand
- `expansion_type` (string, optional): Type ("neighbors", "hierarchy")
- `relationship_types` (array, optional): Filter relationship types
- `max_expansions` (integer, optional): Maximum expansions

#### `find_graph_paths`
Find paths between nodes in the knowledge graph.

**Parameters:**
- `start_node` (string, required): Starting node
- `end_node` (string, required): Target node
- `max_path_length` (integer, optional): Maximum path length
- `algorithm` (string, optional): Path finding algorithm
- `max_paths` (integer, optional): Maximum paths to find

#### `get_node_details`
Get detailed information about a specific node.

**Parameters:**
- `node_id` (string, required): Node identifier

#### `search_graph_nodes`
Search for nodes in the knowledge graph.

**Parameters:**
- `query` (string, required): Search query
- `node_types` (array, optional): Filter by node types
- `max_results` (integer, optional): Maximum results

### 5. Performance Monitoring Tools

#### `stream_performance_metrics`
Stream real-time performance metrics.

**Parameters:**
- `metric_types` (array, optional): Types of metrics to stream
- `interval_seconds` (float, optional): Update interval

**Response:** Streaming performance data

#### `get_system_metrics`
Get current system performance metrics.

**Response:**
```json
{
  "timestamp": **********.0,
  "metrics": {
    "cpu_usage_percent": 45.2,
    "memory_usage_percent": 67.8,
    "disk_usage_percent": 23.1,
    "network_io": {...}
  },
  "status": "healthy"
}
```

#### `get_application_metrics`
Get application-specific performance metrics.

#### `get_performance_alerts`
Get current performance alerts.

#### `generate_performance_report`
Generate comprehensive performance report.

## Server Management Tools

#### `get_server_info`
Get server information and status.

**Response:**
```json
{
  "server_name": "GraphRAG-DeepSearch-MCP",
  "server_version": "0.1.0",
  "enabled_tool_categories": [...],
  "status": "running",
  "active_streams": 5,
  "tool_count": 25
}
```

#### `list_available_tools`
List all available tools with descriptions.

## Error Handling

All tools return errors in this format:

```json
{
  "error": true,
  "error_type": "ValueError",
  "error_message": "Invalid parameter value",
  "tool_name": "coordinate_agents"
}
```

## Streaming Protocol

Streaming responses use Server-Sent Events (SSE) format:

```
data: {"event_type": "result", "data": {...}}
data: {"event_type": "progress", "progress": 50}
data: {"event_type": "completed", "total_results": 10}
```

## Rate Limits

- Default: 60 requests per minute per client
- Streaming connections: 100 concurrent maximum
- Configurable via server settings

## Examples

### Python Client

```python
from mcp_integration.client import mcp_client_context

async with mcp_client_context("http://localhost:8001") as client:
    # Search
    results = await client.search_hybrid("AI applications")
    
    # Streaming
    async for update in client.stream_search_results("machine learning"):
        print(update)
    
    # Cache
    await client.set_cache_entry("key", {"data": "value"})
    cached = await client.get_cache_entry("key")
```

### HTTP Requests

```bash
# Direct HTTP call
curl -X POST "http://localhost:8001/mcp/tools/local_search" \
  -H "Content-Type: application/json" \
  -d '{"query": "artificial intelligence", "max_results": 5}'

# Streaming
curl -N "http://localhost:8001/mcp/stream/coordinate_agents" \
  -H "Accept: text/event-stream" \
  -d '{"query": "How does AI work?", "agents": ["agent1", "agent2"]}'
```

### JavaScript/TypeScript

```javascript
// Using fetch for regular calls
const response = await fetch('http://localhost:8001/mcp/tools/local_search', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ query: 'artificial intelligence', max_results: 5 })
});
const results = await response.json();

// Using EventSource for streaming
const eventSource = new EventSource('http://localhost:8001/mcp/stream/search_results');
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Stream update:', data);
};
```
