#!/usr/bin/env python3
"""
Debug MCP Response Format

This script helps debug the exact response format from MCP tools.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastmcp import Client
from fastmcp.client.transports import SSETransport


async def debug_tool_response():
    """Debug the exact response format from MCP tools"""
    print("🔍 Debugging MCP Tool Response Format...")
    
    try:
        transport = SSETransport(url="http://localhost:8001/mcp/sse")
        client = Client(transport)
        
        async with client:
            print("✅ Connected to MCP server")
            
            # Call local_search and examine the response structure
            print("\n📋 Calling local_search tool...")
            result = await client.call_tool("local_search", {
                "query": "test query",
                "max_results": 2
            })
            
            print(f"Result type: {type(result)}")
            print(f"Result: {result}")
            
            if hasattr(result, '__dict__'):
                print(f"Result attributes: {result.__dict__}")
            
            if hasattr(result, 'content'):
                print(f"Content type: {type(result.content)}")
                print(f"Content: {result.content}")
                
                if isinstance(result.content, list):
                    print(f"Content is list with {len(result.content)} items")
                    for i, item in enumerate(result.content):
                        print(f"  Item {i} type: {type(item)}")
                        print(f"  Item {i}: {item}")
                        if hasattr(item, '__dict__'):
                            print(f"  Item {i} attributes: {item.__dict__}")
                        if hasattr(item, 'text'):
                            print(f"  Item {i} text: {item.text}")
                            
                            # Try to parse as JSON
                            try:
                                import json
                                parsed = json.loads(item.text)
                                print(f"  Item {i} parsed JSON: {parsed}")
                                print(f"  JSON type: {type(parsed)}")
                                if isinstance(parsed, dict):
                                    print(f"  JSON keys: {list(parsed.keys())}")
                            except Exception as e:
                                print(f"  Failed to parse as JSON: {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main debug function"""
    print("🐛 MCP Response Format Debugger")
    print("=" * 40)
    
    success = await debug_tool_response()
    
    if success:
        print("\n✅ Debug completed successfully")
    else:
        print("\n❌ Debug failed")


if __name__ == "__main__":
    asyncio.run(main())
