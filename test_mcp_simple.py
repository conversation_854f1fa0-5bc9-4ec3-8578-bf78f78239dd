#!/usr/bin/env python3
"""
Simple MCP Server Test

This script tests the basic functionality of the MCP server
using direct HTTP requests to verify the server is working.
"""

import asyncio
import httpx
import json


async def test_server_health():
    """Test basic server health"""
    print("🔍 Testing server health...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8001/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Server Health: {data['status']}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False


async def test_server_info():
    """Test server info endpoint"""
    print("📋 Testing server info...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8001/")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Server Info: {data['message']}")
                print(f"   Version: {data['version']}")
                print(f"   Docs: http://localhost:8001{data['docs']}")
                print(f"   MCP Endpoint: {data['mcp_endpoint']}")
                return True
            else:
                print(f"❌ Server info failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Server info error: {e}")
            return False


async def test_api_docs():
    """Test API documentation endpoint"""
    print("📖 Testing API documentation...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8001/docs")
            if response.status_code == 200:
                print("✅ API Documentation: Available at http://localhost:8001/docs")
                return True
            else:
                print(f"❌ API docs failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API docs error: {e}")
            return False


async def test_mcp_endpoint():
    """Test MCP endpoint availability"""
    print("🔧 Testing MCP endpoint...")
    
    async with httpx.AsyncClient() as client:
        try:
            # Test the SSE endpoint
            response = await client.get("http://localhost:8001/mcp/sse")
            print(f"   SSE endpoint status: {response.status_code}")
            
            # Test the messages endpoint
            response = await client.get("http://localhost:8001/mcp/messages")
            print(f"   Messages endpoint status: {response.status_code}")
            
            return True
        except Exception as e:
            print(f"❌ MCP endpoint error: {e}")
            return False


async def test_fastmcp_direct():
    """Test FastMCP server directly"""
    print("🚀 Testing FastMCP server directly...")
    
    try:
        # Import the MCP server components
        import sys
        from pathlib import Path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from mcp_integration.server import GraphRAGMCPServer
        from mcp_integration.config import MCPConfig
        
        # Create a test configuration
        config = MCPConfig(
            server_name="Test-MCP-Server",
            enable_streaming=True,
            enabled_tool_categories=["agent_coordination", "search_tools"]
        )
        
        # Create the MCP server
        mcp_server = GraphRAGMCPServer(config)
        
        # Get the FastMCP app
        mcp_app = mcp_server.get_mcp_app()
        
        # Check available tools
        tools = await mcp_app.get_tools()
        print(f"✅ FastMCP Tools Available: {len(tools)} tools")
        
        # List some tools
        for i, (tool_name, tool) in enumerate(list(tools.items())[:5]):
            print(f"   - {tool_name}: {getattr(tool, 'description', 'No description')}")
        
        if len(tools) > 5:
            print(f"   ... and {len(tools) - 5} more tools")
        
        return True
        
    except Exception as e:
        print(f"❌ FastMCP direct test error: {e}")
        return False


async def main():
    """Main test function"""
    print("🧪 Simple MCP Server Test")
    print("=" * 40)
    
    # Test basic server functionality
    tests = [
        ("Server Health", test_server_health),
        ("Server Info", test_server_info),
        ("API Documentation", test_api_docs),
        ("MCP Endpoint", test_mcp_endpoint),
        ("FastMCP Direct", test_fastmcp_direct)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! MCP server is working correctly.")
        print("\n💡 Next steps:")
        print("   1. Open http://localhost:8001/docs to explore the API")
        print("   2. Use the FastMCP tools directly in your application")
        print("   3. Integrate with your existing GraphRAG system")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    asyncio.run(main())
