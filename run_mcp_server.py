#!/usr/bin/env python3
"""
GraphRAG MCP Server Startup Script

This script starts the MCP server for GraphRAG + DeepSearch integration.
Run this in Cursor to start the MCP server with real-time capabilities.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mcp_integration.server import GraphRAGMCPServer, create_graphrag_mcp_server
from mcp_integration.config import MCPConfig
from fastapi import FastAPI
import uvicorn


def setup_logging():
    """Setup logging for the MCP server"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('mcp_server.log')
        ]
    )


def create_mcp_app() -> FastAPI:
    """Create FastAPI app with MCP integration"""
    
    # Create MCP configuration first to get lifespan
    config = MCPConfig(
        server_name="GraphRAG-DeepSearch-MCP",
        host="localhost",
        port=8001,
        enable_streaming=True,
        enable_agent_coordination=True,
        enable_distributed_cache=True,
        enable_performance_monitoring=True,
        log_level="INFO"
    )

    # TDD FIX 1: Create single MCP server instance that will be reused
    from mcp_integration.server import GraphRAGMCPServer
    mcp_server_instance = GraphRAGMCPServer(config)

    # TDD FIX 2: Get the FastMCP app and create HTTP app for lifespan
    mcp_fastmcp_app = mcp_server_instance.get_mcp_app()

    # TDD FIX 3: Create Streamable HTTP app to get the proper lifespan
    # Store this app to reuse it during mounting to avoid creating different instances
    mcp_http_app = mcp_fastmcp_app.http_app(
        path="/",
        transport="streamable-http"
    )

    # TDD FIX 4: Create custom lifespan that combines MCP lifespan with background tasks
    from contextlib import asynccontextmanager

    @asynccontextmanager
    async def combined_lifespan(app: FastAPI):
        # Start MCP lifespan
        async with mcp_http_app.lifespan(app):
            # Start background tasks
            if hasattr(app.state, 'mcp_server'):
                await app.state.mcp_server.start_background_tasks()
            yield

    # Create FastAPI app with combined lifespan
    app = FastAPI(
        title="GraphRAG + DeepSearch MCP Server",
        description="Model Context Protocol server for GraphRAG + DeepSearch system",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=combined_lifespan  # Use the combined lifespan
    )

    # TDD FIX 5: Store the HTTP app in the server instance for reuse
    mcp_server_instance._cached_http_app = mcp_http_app
    
    # Add basic health check
    @app.get("/")
    async def root():
        return {
            "message": "GraphRAG MCP Server is running!",
            "version": "0.1.0",
            "docs": "/docs",
            "mcp_endpoint": "/mcp"
        }
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "GraphRAG-MCP-Server"}

    # TDD FIX 4: Integrate the existing MCP server instance (don't create a new one)
    mcp_server_instance.integrate_with_fastapi(app)

    # TDD FIX 7: Store the server instance for background task startup in lifespan
    app.state.mcp_server = mcp_server_instance

    return app


async def main():
    """Main function to run the MCP server"""
    setup_logging()
    logger = logging.getLogger("mcp_server_startup")
    
    logger.info("🚀 Starting GraphRAG MCP Server...")
    
    try:
        # Create the app
        app = await create_mcp_app()
        
        # Configuration
        config = uvicorn.Config(
            app,
            host="localhost",
            port=8001,
            log_level="info",
            reload=False,  # Set to True for development
            access_log=True
        )
        
        # Create and run server
        server = uvicorn.Server(config)
        
        logger.info("✅ MCP Server starting on http://localhost:8001")
        logger.info("📖 API Documentation: http://localhost:8001/docs")
        logger.info("🔧 MCP Endpoint: http://localhost:8001/mcp")
        logger.info("❤️  Health Check: http://localhost:8001/health")
        
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        raise


if __name__ == "__main__":
    # Install dependencies if needed
    try:
        import fastmcp
        import httpx
        import psutil
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Installing MCP dependencies...")
        import subprocess
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "fastmcp>=2.9.0", "httpx>=0.28.0", "websockets>=11.0.0", 
            "sse-starlette>=2.3.0", "psutil>=5.9.0"
        ])
        print("✅ Dependencies installed!")
    
    # Run the server
    asyncio.run(main())
