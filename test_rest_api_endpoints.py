#!/usr/bin/env python3
"""
Comprehensive Test Suite for MCP REST API Endpoints

Tests all REST API endpoints that can trigger MCP tools via both
Streamable HTTP and SSE transports.
"""

import asyncio
import json
import requests
import time
from typing import Dict, Any, List
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class MCPRestAPITester:
    """Comprehensive tester for MCP REST API endpoints"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1/mcp"
        self.results = []
        
    def test_endpoint(self, method: str, endpoint: str, data: Dict[str, Any] = None, 
                     params: Dict[str, Any] = None, description: str = "") -> Dict[str, Any]:
        """Test a single REST API endpoint"""
        url = f"{self.api_base}{endpoint}"
        
        try:
            start_time = time.time()
            
            if method.upper() == "GET":
                response = requests.get(url, params=params, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, params=params, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            execution_time = (time.time() - start_time) * 1000
            
            result = {
                "method": method.upper(),
                "endpoint": endpoint,
                "description": description,
                "status_code": response.status_code,
                "success": response.status_code < 400,
                "execution_time_ms": execution_time,
                "response_size": len(response.content),
                "error": None
            }
            
            if response.status_code < 400:
                try:
                    result["response"] = response.json()
                except json.JSONDecodeError:
                    result["response"] = response.text
            else:
                result["error"] = response.text
                
            return result
            
        except Exception as e:
            return {
                "method": method.upper(),
                "endpoint": endpoint,
                "description": description,
                "status_code": 0,
                "success": False,
                "execution_time_ms": 0,
                "response_size": 0,
                "error": str(e),
                "response": None
            }
    
    def run_all_tests(self) -> List[Dict[str, Any]]:
        """Run comprehensive test suite"""
        console.print(Panel.fit("🧪 MCP REST API Comprehensive Test Suite", style="bold blue"))
        
        tests = [
            # Health and status endpoints
            ("GET", "/health", None, None, "MCP server health check"),
            ("GET", "/tools", None, {"transport": "streamable-http"}, "List tools (Streamable HTTP)"),
            ("GET", "/tools", None, {"transport": "sse"}, "List tools (SSE)"),
            
            # Tool information
            ("GET", "/tools/local_search", None, {"transport": "streamable-http"}, "Get local_search tool info"),
            ("GET", "/tools/get_system_metrics", None, {"transport": "streamable-http"}, "Get system metrics tool info"),
            
            # Tool execution - Basic tools
            ("POST", "/tools/call", {
                "tool_name": "local_search",
                "arguments": {"query": "test query", "max_results": 2},
                "transport": "streamable-http"
            }, None, "Call local_search (Streamable HTTP)"),
            
            ("POST", "/tools/call", {
                "tool_name": "local_search",
                "arguments": {"query": "test query", "max_results": 2},
                "transport": "sse"
            }, None, "Call local_search (SSE)"),
            
            ("POST", "/tools/call", {
                "tool_name": "get_system_metrics",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "Get system metrics"),
            
            ("POST", "/tools/call", {
                "tool_name": "get_application_metrics",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "Get application metrics"),
            
            # Cache operations
            ("POST", "/cache/set", {
                "cache_key": "rest_api_test",
                "value": {"test": True, "timestamp": time.time()},
                "cache_type": "test_cache"
            }, {"transport": "streamable-http"}, "Set cache entry"),
            
            ("GET", "/cache/rest_api_test", None, {
                "cache_type": "test_cache",
                "transport": "streamable-http"
            }, "Get cache entry"),
            
            # Search convenience endpoint
            ("POST", "/search", {
                "query": "artificial intelligence",
                "max_results": 3
            }, {"transport": "streamable-http"}, "Convenience search endpoint"),
            
            # Agent status
            ("GET", "/agent/test_agent/status", None, {
                "transport": "streamable-http"
            }, "Get agent status"),
            
            # Knowledge graph operations
            ("POST", "/tools/call", {
                "tool_name": "explore_knowledge_graph",
                "arguments": {
                    "start_node": "test_node",
                    "max_depth": 2,
                    "max_nodes": 5
                },
                "transport": "streamable-http"
            }, None, "Explore knowledge graph"),
            
            ("POST", "/tools/call", {
                "tool_name": "get_graph_statistics",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "Get graph statistics"),
            
            # Performance monitoring
            ("POST", "/tools/call", {
                "tool_name": "get_performance_alerts",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "Get performance alerts"),
            
            # Server information
            ("POST", "/tools/call", {
                "tool_name": "get_server_info",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "Get server info"),
            
            ("POST", "/tools/call", {
                "tool_name": "list_available_tools",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "List available tools via MCP"),
            
            # Error handling tests
            ("POST", "/tools/call", {
                "tool_name": "nonexistent_tool",
                "arguments": {},
                "transport": "streamable-http"
            }, None, "Test nonexistent tool (should fail)"),
            
            ("GET", "/tools/nonexistent_tool", None, {
                "transport": "streamable-http"
            }, "Get nonexistent tool info (should fail)"),
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running tests...", total=len(tests))
            
            for method, endpoint, data, params, description in tests:
                progress.update(task, description=f"Testing: {description}")
                result = self.test_endpoint(method, endpoint, data, params, description)
                self.results.append(result)
                progress.advance(task)
                time.sleep(0.1)  # Small delay to see progress
        
        return self.results
    
    def print_results(self):
        """Print comprehensive test results"""
        console.print("\n" + "="*80)
        console.print(Panel.fit("📊 Test Results Summary", style="bold green"))
        
        # Summary statistics
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - successful_tests
        
        summary_table = Table(title="Test Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="magenta")
        
        summary_table.add_row("Total Tests", str(total_tests))
        summary_table.add_row("Successful", str(successful_tests))
        summary_table.add_row("Failed", str(failed_tests))
        summary_table.add_row("Success Rate", f"{(successful_tests/total_tests)*100:.1f}%")
        
        console.print(summary_table)
        
        # Detailed results
        console.print("\n📋 Detailed Results:")
        
        results_table = Table()
        results_table.add_column("Method", style="cyan")
        results_table.add_column("Endpoint", style="blue")
        results_table.add_column("Description", style="white")
        results_table.add_column("Status", style="green")
        results_table.add_column("Time (ms)", style="yellow")
        results_table.add_column("Size (bytes)", style="magenta")
        
        for result in self.results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            if not result["success"] and "nonexistent" in result["description"]:
                status = "✅ EXPECTED FAIL"
            
            results_table.add_row(
                result["method"],
                result["endpoint"],
                result["description"][:40] + "..." if len(result["description"]) > 40 else result["description"],
                status,
                f"{result['execution_time_ms']:.1f}",
                str(result["response_size"])
            )
        
        console.print(results_table)
        
        # Show sample responses for successful tests
        console.print("\n🔍 Sample Successful Responses:")
        
        successful_results = [r for r in self.results if r["success"] and r["response"]][:3]
        for i, result in enumerate(successful_results, 1):
            console.print(f"\n{i}. {result['description']}:")
            if isinstance(result["response"], dict):
                # Show truncated response
                response_str = json.dumps(result["response"], indent=2)
                if len(response_str) > 500:
                    response_str = response_str[:500] + "\n  ... (truncated)"
                console.print(f"[dim]{response_str}[/dim]")
            else:
                console.print(f"[dim]{str(result['response'])[:200]}...[/dim]")
        
        # Show errors for failed tests (excluding expected failures)
        failed_results = [r for r in self.results if not r["success"] and "nonexistent" not in r["description"]]
        if failed_results:
            console.print("\n❌ Failed Tests (Unexpected):")
            for result in failed_results:
                console.print(f"• {result['description']}: {result['error']}")
        
        console.print(f"\n🎯 Test completed! {successful_tests}/{total_tests} tests passed.")


def main():
    """Main test execution"""
    console.print("🚀 Starting MCP REST API Test Suite...")
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            console.print("❌ MCP server is not running or not healthy!")
            console.print("Please start the server with: uvicorn run_mcp_server:create_mcp_app --host localhost --port 8001 --factory")
            return
    except requests.exceptions.RequestException:
        console.print("❌ Cannot connect to MCP server at http://localhost:8001")
        console.print("Please start the server with: uvicorn run_mcp_server:create_mcp_app --host localhost --port 8001 --factory")
        return
    
    console.print("✅ MCP server is running and healthy!")
    
    # Run tests
    tester = MCPRestAPITester()
    tester.run_all_tests()
    tester.print_results()


if __name__ == "__main__":
    main()