#!/usr/bin/env python3
"""
Correct MCP Client Test

This script demonstrates the CORRECT way to interact with our FastMCP server
using the MCP protocol (not REST endpoints).
"""

import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastmcp import Client
from fastmcp.client.transports import SSETransport
import json


def parse_mcp_result(result):
    """Helper function to parse MCP tool results"""
    try:
        # FastMCP returns a list of TextContent objects
        if isinstance(result, list) and len(result) > 0:
            # Get the first content item
            content_item = result[0]
            if hasattr(content_item, 'text'):
                # Parse JSON from text content
                return json.loads(content_item.text)
            else:
                return content_item
        elif hasattr(result, 'content'):
            if isinstance(result.content, list):
                # Get the first content item
                content_item = result.content[0]
                if hasattr(content_item, 'text'):
                    # Parse JSON from text content
                    return json.loads(content_item.text)
                else:
                    return content_item
            else:
                if hasattr(result.content, 'text'):
                    return json.loads(result.content.text)
                else:
                    return result.content
        else:
            return result
    except (json.JSONDecodeError, AttributeError, IndexError) as e:
        # If parsing fails, return the raw result for debugging
        return {"error": f"Failed to parse result: {e}", "raw_result": str(result)}


async def test_mcp_protocol_connection():
    """Test connection using MCP protocol via SSE transport"""
    print("🔌 Testing MCP Protocol Connection...")
    
    try:
        # Create SSE transport for our server
        transport = SSETransport(url="http://localhost:8001/mcp/sse")
        client = Client(transport)
        
        async with client:
            # Test basic connection
            await client.ping()
            print("✅ MCP Protocol: Connection successful")
            
            # List available tools
            tools = await client.list_tools()
            print(f"✅ Available Tools: {len(tools)} tools found")
            
            # Show first few tools
            for i, tool in enumerate(tools[:5]):
                print(f"   - {tool.name}: {tool.description}")
            
            if len(tools) > 5:
                print(f"   ... and {len(tools) - 5} more tools")
            
            return True
            
    except Exception as e:
        print(f"❌ MCP Protocol connection failed: {e}")
        return False


async def test_local_search_tool():
    """Test the local_search tool using MCP protocol"""
    print("\n🔍 Testing local_search Tool...")
    
    try:
        transport = SSETransport(url="http://localhost:8001/mcp/sse")
        client = Client(transport)
        
        async with client:
            # Call the local_search tool
            result = await client.call_tool("local_search", {
                "query": "artificial intelligence",
                "max_results": 5
            })
            
            print("✅ local_search Tool: Success")
            print(f"   Query: artificial intelligence")

            # Parse the result using our helper function
            content = parse_mcp_result(result)

            if content.get('error'):
                print(f"   ⚠️  Parse Error: {content['error']}")
                print(f"   Raw Result: {content.get('raw_result', 'N/A')}")
            else:
                print(f"   Results: {len(content.get('results', []))} found")
                print(f"   Search Type: {content.get('search_type', 'unknown')}")
                print(f"   Execution Time: {content.get('execution_time', 0):.3f}s")

                # Show first result
                results = content.get('results', [])
                if results:
                    first_result = results[0]
                    print(f"   First Result: {first_result.get('content', 'No content')[:50]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ local_search tool failed: {e}")
        return False


async def test_agent_coordination_tool():
    """Test the coordinate_agents tool"""
    print("\n🤖 Testing coordinate_agents Tool...")
    
    try:
        transport = SSETransport(url="http://localhost:8001/mcp/sse")
        client = Client(transport)
        
        async with client:
            # Call the coordinate_agents tool (this is a streaming tool)
            print("   Starting agent coordination...")
            
            # Note: coordinate_agents returns a streaming response
            # For now, let's test a non-streaming tool
            result = await client.call_tool("get_agent_status", {
                "agent_id": "naive_rag_agent"
            })
            
            print("✅ get_agent_status Tool: Success")

            # Parse the result using our helper function
            content = parse_mcp_result(result)

            if content.get('error'):
                print(f"   ⚠️  Parse Error: {content['error']}")
            else:
                print(f"   Agent ID: {content.get('agent_id', 'unknown')}")
                print(f"   Status: {content.get('status', 'unknown')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Agent coordination tool failed: {e}")
        return False


async def test_cache_tools():
    """Test cache management tools"""
    print("\n💾 Testing Cache Tools...")
    
    try:
        transport = SSETransport(url="http://localhost:8001/mcp/sse")
        client = Client(transport)
        
        async with client:
            # Test cache set
            set_result = await client.call_tool("set_cache_entry", {
                "cache_key": "test_key_mcp",
                "value": {"message": "Hello from MCP!", "timestamp": "2024-01-01"},
                "cache_type": "test_cache"
            })
            
            print("✅ set_cache_entry Tool: Success")

            # Parse the set result
            set_content = parse_mcp_result(set_result)

            if set_content.get('error'):
                print(f"   ⚠️  Parse Error: {set_content['error']}")
            else:
                print(f"   Cache Key: {set_content.get('cache_key', 'unknown')}")
                print(f"   Success: {set_content.get('success', False)}")

            # Test cache get
            get_result = await client.call_tool("get_cache_entry", {
                "cache_key": "test_key_mcp",
                "cache_type": "test_cache"
            })

            print("✅ get_cache_entry Tool: Success")

            # Parse the get result
            get_content = parse_mcp_result(get_result)

            if get_content.get('error'):
                print(f"   ⚠️  Parse Error: {get_content['error']}")
            else:
                print(f"   Found: {get_content.get('found', False)}")
                if get_content.get('found'):
                    value = get_content.get('value', {})
                    print(f"   Value: {value}")
            
            return True
            
    except Exception as e:
        print(f"❌ Cache tools failed: {e}")
        return False


async def test_server_management_tools():
    """Test server management tools"""
    print("\n🔧 Testing Server Management Tools...")

    try:
        transport = SSETransport(url="http://localhost:8001/mcp/sse")
        client = Client(transport)

        async with client:
            # List all available tools first
            tools = await client.list_tools()
            print(f"✅ Available Tools: {len(tools)} tools found")

            # Look for server management tools
            server_tools = [tool for tool in tools if 'server' in tool.name.lower() or 'info' in tool.name.lower()]

            if server_tools:
                print("   Server-related tools found:")
                for tool in server_tools:
                    print(f"     - {tool.name}: {tool.description}")

                # Try to call the first server tool
                first_tool = server_tools[0]
                try:
                    result = await client.call_tool(first_tool.name)
                    print(f"✅ {first_tool.name} Tool: Success")

                    # Handle result format
                    if hasattr(result, 'content'):
                        content = result.content
                    else:
                        content = result
                    if isinstance(content, list) and content:
                        content = content[0]

                    print(f"   Result keys: {list(content.keys()) if isinstance(content, dict) else 'Not a dict'}")

                except Exception as e:
                    print(f"   ⚠️  Could not call {first_tool.name}: {e}")
            else:
                print("   No server management tools found")

            # Show tool categories
            categories = {}
            for tool in tools:
                # Try to infer category from tool name
                name = tool.name.lower()
                if 'agent' in name:
                    category = 'agent_coordination'
                elif 'search' in name:
                    category = 'search_tools'
                elif 'cache' in name:
                    category = 'cache_tools'
                elif 'graph' in name or 'node' in name:
                    category = 'knowledge_graph'
                elif 'performance' in name or 'metric' in name:
                    category = 'performance_monitoring'
                else:
                    category = 'other'

                if category not in categories:
                    categories[category] = 0
                categories[category] += 1

            print("   Tool Categories:")
            for category, count in categories.items():
                print(f"     - {category}: {count} tools")

            return True

    except Exception as e:
        print(f"❌ Server management tools failed: {e}")
        return False


async def main():
    """Main test function"""
    print("🧪 Correct MCP Client Test Suite")
    print("=" * 50)
    print("Testing MCP Protocol (NOT REST endpoints)")
    print()
    
    # Run all tests
    tests = [
        ("MCP Protocol Connection", test_mcp_protocol_connection),
        ("Local Search Tool", test_local_search_tool),
        ("Agent Coordination Tool", test_agent_coordination_tool),
        ("Cache Tools", test_cache_tools),
        ("Server Management Tools", test_server_management_tools)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All MCP tests passed!")
        print("\n💡 Key Takeaways:")
        print("   ✅ Use MCP protocol, NOT REST endpoints")
        print("   ✅ Connect via SSE transport: http://localhost:8001/mcp/sse")
        print("   ✅ Use fastmcp.Client with SSETransport")
        print("   ✅ Call tools with client.call_tool(tool_name, parameters)")
        print("   ✅ All 14+ MCP tools are working correctly")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    asyncio.run(main())
